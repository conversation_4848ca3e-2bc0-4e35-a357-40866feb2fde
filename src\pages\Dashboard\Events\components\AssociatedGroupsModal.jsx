import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Toast } from 'primereact/toast';
import { motion } from 'framer-motion';
import { FiUsers, FiInfo, FiPlus, FiUserPlus } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';
import axiosInstance from '../../../../config/Axios';
import {
    mockAvailableEventGroups,
    mockAvailableEventMembers,
    mockEventCardTypes,
    getAvailableEventGroupsForEvent,
    getAvailableEventMembersForEvent
} from '@data/mockEventGroupsData';

const AssociatedGroupsModal = ({ visible, onHide, event, onUpdateEvent }) => {
    const { isMobile } = useLayout();

    // State for modals and data
    const [showAddGroupsModal, setShowAddGroupsModal] = useState(false);
    const [showAddMembersModal, setShowAddMembersModal] = useState(false);
    const [selectedGroupForMembers, setSelectedGroupForMembers] = useState(null);
    const [availableGroups, setAvailableGroups] = useState([]);
    const [availableMembers, setAvailableMembers] = useState([]);
    const [loading, setLoading] = useState(false);

    if (!event) return null;

    const groups = event.associatedGroups || [];

    // Fetch available event groups for selection
    const fetchAvailableEventGroups = async () => {
        try {
            setLoading(true);
            // Use event-specific endpoint to get available event groups
            const response = await axiosInstance.get(`/events/${event.id}/available-groups`);
            const allEventGroups = Array.isArray(response.data) ? response.data : (response.data.data || []);

            // Filter out groups that are already associated with the event
            const associatedGroupIds = groups.map(g => g.id);
            const filteredGroups = allEventGroups.filter(group => !associatedGroupIds.includes(group.id));

            setAvailableGroups(filteredGroups);
        } catch (error) {
            console.error('Error fetching event groups:', error);
            // Fallback: use mock data for development
            const mockGroups = getAvailableEventGroupsForEvent(event.id);
            const associatedGroupIds = groups.map(g => g.id);
            const filteredMockGroups = mockGroups.filter(group => !associatedGroupIds.includes(group.id));
            setAvailableGroups(filteredMockGroups);
        } finally {
            setLoading(false);
        }
    };

    // Fetch available members for event group selection
    const fetchAvailableMembers = async () => {
        try {
            setLoading(true);
            // Use event-specific endpoint to get members available for event groups
            const response = await axiosInstance.get(`/events/${event.id}/available-members`);
            const allMembers = Array.isArray(response.data) ? response.data : (response.data.data || []);
            setAvailableMembers(allMembers);
        } catch (error) {
            console.error('Error fetching event members:', error);
            // Fallback: use mock data for development
            const mockMembers = getAvailableEventMembersForEvent(event.id);
            setAvailableMembers(mockMembers);
        } finally {
            setLoading(false);
        }
    };

    // Handle adding event groups to event
    const handleAddEventGroupsToEvent = async (selectedGroups) => {
        try {
            setLoading(true);

            // Use event-specific API to associate groups with the event
            const payload = {
                event_id: event.id,
                group_ids: selectedGroups.map(group => group.id)
            };

            await axiosInstance.post(`/events/${event.id}/groups`, payload);

            // Update the local event data
            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, ...selectedGroups]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddGroupsModal(false);
        } catch (error) {
            console.error('Error adding event groups to event:', error);
            // For development, still update locally even if API fails
            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, ...selectedGroups]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddGroupsModal(false);
        } finally {
            setLoading(false);
        }
    };

    // Handle adding members to a specific event group
    const handleAddMembersToEventGroup = async (selectedMembers) => {
        try {
            setLoading(true);

            if (!selectedGroupForMembers) return;

            const payload = {
                event_id: event.id,
                group_id: selectedGroupForMembers.id,
                user_ids: selectedMembers.map(member => member.id)
            };

            // Call event-specific API to add members to event group
            await axiosInstance.post(`/event-groups/${selectedGroupForMembers.id}/members`, payload);

            // Update the group's member count in the local state
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + selectedMembers.length }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddMembersModal(false);
            setSelectedGroupForMembers(null);
        } catch (error) {
            console.error('Error adding members to event group:', error);
            // For development, still update locally even if API fails
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + selectedMembers.length }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddMembersModal(false);
            setSelectedGroupForMembers(null);
        } finally {
            setLoading(false);
        }
    };

    // Handle creating new event group
    const handleCreateEventGroup = async (groupData) => {
        try {
            setLoading(true);

            const payload = {
                event_id: event.id,
                title: groupData.title,
                description: groupData.description,
                card_type_name: groupData.cardType,
                access_level: groupData.accessLevel,
                status: 'active',
                event_context: true
            };

            // Call event-specific API to create new group
            const response = await axiosInstance.post(`/events/${event.id}/groups/create`, payload);
            const newGroup = response.data.group || response.data;

            // Add the new group to available groups list
            setAvailableGroups(prev => [...prev, newGroup]);

            return newGroup;
        } catch (error) {
            console.error('Error creating event group:', error);
            // For development, create a mock group
            const mockGroup = {
                id: `evt-grp-new-${Date.now()}`,
                title: groupData.title,
                description: groupData.description,
                card_type_name: groupData.cardType,
                access_level: groupData.accessLevel,
                status: 'active',
                memberCount: 0,
                event_context: true,
                created_at: new Date().toISOString()
            };

            setAvailableGroups(prev => [...prev, mockGroup]);
            return mockGroup;
        } finally {
            setLoading(false);
        }
    };

    // Handle creating new event member
    const handleCreateEventMember = async (memberData) => {
        try {
            setLoading(true);

            const payload = {
                event_id: event.id,
                name: memberData.name,
                email: memberData.email,
                department: memberData.department,
                role: memberData.role,
                access_level: memberData.accessLevel,
                event_context: true
            };

            // Call event-specific API to create new member
            const response = await axiosInstance.post(`/events/${event.id}/members/create`, payload);
            const newMember = response.data.member || response.data;

            // Add the new member to available members list
            setAvailableMembers(prev => [...prev, newMember]);

            return newMember;
        } catch (error) {
            console.error('Error creating event member:', error);
            // For development, create a mock member
            const mockMember = {
                id: `evt-mem-new-${Date.now()}`,
                name: memberData.name,
                email: memberData.email,
                department: memberData.department,
                role: memberData.role,
                access_level: memberData.accessLevel,
                event_context: true,
                image: null
            };

            setAvailableMembers(prev => [...prev, mockMember]);
            return mockMember;
        } finally {
            setLoading(false);
        }
    };

    // Template functions for DataTable columns
    const groupNameTemplate = (rowData) => (
        <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white font-medium">
                    {rowData.title?.charAt(0)?.toUpperCase()}
                </span>
            </div>
            <div>
                <div className="font-medium">{rowData.title}</div>
                <div className="text-sm text-gray-500">{rowData.description}</div>
            </div>
        </div>
    );

    const cardTypeTemplate = (rowData) => (
        <Tag
            value={rowData.card_type_name}
            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
        />
    );

    const statusTemplate = (rowData) => {
        const statusColors = {
            active: '#22C55E',
            inactive: '#6B7280',
            pending: '#F59E0B'
        };
        
        return (
            <Tag
                value={rowData.status}
                style={{ backgroundColor: statusColors[rowData.status] || '#6B7280' }}
                className="text-white px-3 py-1 rounded-full text-sm capitalize"
            />
        );
    };

    const memberCountTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiUsers size={14} className="text-gray-500" />
            <span className="text-sm font-medium">
                {rowData.memberCount} {rowData.memberCount === 1 ? 'member' : 'members'}
            </span>
        </div>
    );

    const actionsTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <Button
                icon={<FiUserPlus size={14} />}
                className="p-button-sm p-button-outlined"
                style={{
                    backgroundColor: 'white',
                    color: 'black',
                    border: '1px solid #d1d5db',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-1px)';
                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                }}
                onClick={() => {
                    setSelectedGroupForMembers(rowData);
                    fetchAvailableMembers();
                    setShowAddMembersModal(true);
                }}
                tooltip="Add Members"
                tooltipOptions={{ position: 'top' }}
            />
        </div>
    );

    // Mobile view for groups
    const MobileGroupsList = () => (
        <div className="space-y-4">
            {groups.map((group) => (
                <motion.div
                    key={group.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border border-gray-200 rounded-lg p-4 bg-white"
                >
                    <div className="flex items-start gap-3 mb-3">
                        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                                {group.title?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                        <div className="flex-1">
                            <div className="flex items-start justify-between mb-1">
                                <h3 className="font-semibold text-lg text-gray-900">{group.title}</h3>
                                <Button
                                    icon={<FiUserPlus size={16} />}
                                    className="p-button-sm p-button-outlined"
                                    style={{
                                        backgroundColor: 'white',
                                        color: 'black',
                                        border: '1px solid #d1d5db',
                                        padding: '8px 10px',
                                        borderRadius: '6px',
                                        minWidth: 'auto',
                                        minHeight: '44px', // Better touch target for mobile
                                        transition: 'all 0.2s ease'
                                    }}
                                    onClick={() => {
                                        setSelectedGroupForMembers(group);
                                        fetchAvailableMembers();
                                        setShowAddMembersModal(true);
                                    }}
                                    tooltip="Add Members"
                                    tooltipOptions={{ position: 'top' }}
                                />
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                            <div className="flex items-center gap-2 mb-2">
                                <Tag
                                    value={group.card_type_name}
                                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                                />
                                <Tag
                                    value={group.status}
                                    style={{
                                        backgroundColor: group.status === 'active' ? '#22C55E' : '#6B7280'
                                    }}
                                    className="text-white px-2 py-1 rounded-full text-xs capitalize"
                                />
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiUsers size={14} />
                                <span>{group.memberCount} {group.memberCount === 1 ? 'member' : 'members'}</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
            ))}
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        <FiUsers className="text-blue-600" size={20} />
                        <span>Associated Groups - {event.name}</span>
                    </div>
                    <Button
                        icon={<FiPlus size={16} />}
                        label={isMobile ? "" : "Add Groups"}
                        className="p-button-sm"
                        style={{
                            backgroundColor: 'white',
                            color: 'black',
                            border: '1px solid #d1d5db',
                            padding: isMobile ? '8px' : '6px 12px',
                            borderRadius: '6px',
                            transition: 'all 0.2s ease',
                            minHeight: '44px', // Better touch target for mobile
                            minWidth: isMobile ? '44px' : 'auto'
                        }}
                        onMouseEnter={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                            }
                        }}
                        onClick={() => {
                            fetchAvailableEventGroups();
                            setShowAddGroupsModal(true);
                        }}
                        tooltip="Add Groups to Event"
                        tooltipOptions={{ position: 'bottom' }}
                    />
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '70vw',
                maxWidth: isMobile ? '95vw' : '1000px',
                height: isMobile ? '90vh' : 'auto',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '95vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="associated-groups-modal"
            contentStyle={{
                height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
        >
            <div className="space-y-4">
                {/* Event Info */}
                <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                    <div className="flex items-center gap-2 mb-2">
                        <FiInfo className="text-blue-600" size={16} />
                        <span className="font-medium text-blue-900">Event Information</span>
                    </div>
                    <div className="text-sm text-blue-800">
                        <p><strong>Event:</strong> {event.name}</p>
                        <p><strong>Location:</strong> {event.location}</p>
                        <p><strong>Date:</strong> {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}</p>
                        <p><strong>Total Groups:</strong> {groups.length}</p>
                    </div>
                </motion.div>

                {/* Groups Content */}
                {groups.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="text-center py-8"
                    >
                        <FiUsers size={48} className="text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No Associated Groups</h3>
                        <p className="text-gray-400">This event has no groups associated with it yet.</p>
                    </motion.div>
                ) : (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        {isMobile ? (
                            <MobileGroupsList />
                        ) : (
                            <DataTable
                                value={groups}
                                className="border border-gray-200 rounded-lg"
                                emptyMessage="No groups found"
                                responsiveLayout="stack"
                                breakpoint="960px"
                            >
                                <Column
                                    body={groupNameTemplate}
                                    header="Group Name"
                                    style={{ minWidth: '250px' }}
                                />
                                <Column
                                    body={cardTypeTemplate}
                                    header="Card Type"
                                    style={{ minWidth: '150px' }}
                                />
                                <Column
                                    body={statusTemplate}
                                    header="Status"
                                    style={{ minWidth: '100px' }}
                                />
                                <Column
                                    body={memberCountTemplate}
                                    header="Members"
                                    style={{ minWidth: '120px' }}
                                />
                                <Column
                                    body={actionsTemplate}
                                    header="Actions"
                                    style={{ minWidth: '100px' }}
                                    exportable={false}
                                />
                            </DataTable>
                        )}
                    </motion.div>
                )}
            </div>

            {/* Group Selection Modal */}
            <Dialog
                header="Add Groups to Event"
                visible={showAddGroupsModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => setShowAddGroupsModal(false)}
                className="add-groups-modal"
                modal
            >
                <div className="space-y-4">
                    <div className="text-sm text-gray-600 mb-4">
                        Select groups to associate with <strong>{event.name}</strong>
                    </div>

                    <GroupSelectionContent
                        availableGroups={availableGroups}
                        loading={loading}
                        onConfirm={handleAddEventGroupsToEvent}
                        onCancel={() => setShowAddGroupsModal(false)}
                        onCreateGroup={handleCreateEventGroup}
                        eventCardTypes={mockEventCardTypes}
                        isMobile={isMobile}
                    />
                </div>
            </Dialog>

            {/* Member Selection Modal */}
            <Dialog
                header={`Add Members to ${selectedGroupForMembers?.title || 'Group'}`}
                visible={showAddMembersModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => {
                    setShowAddMembersModal(false);
                    setSelectedGroupForMembers(null);
                }}
                className="add-members-modal"
                modal
            >
                <div className="space-y-4">
                    <div className="text-sm text-gray-600 mb-4">
                        Select members to add to <strong>{selectedGroupForMembers?.title}</strong>
                    </div>

                    <MemberSelectionContent
                        availableMembers={availableMembers}
                        loading={loading}
                        onConfirm={handleAddMembersToEventGroup}
                        onCancel={() => {
                            setShowAddMembersModal(false);
                            setSelectedGroupForMembers(null);
                        }}
                        onCreateMember={handleCreateEventMember}
                        isMobile={isMobile}
                    />
                </div>
            </Dialog>
        </Dialog>
    );
};

// Group Selection Content Component
const GroupSelectionContent = ({ availableGroups, loading, onConfirm, onCancel, onCreateGroup, eventCardTypes, isMobile }) => {
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [createFormData, setCreateFormData] = useState({
        title: '',
        description: '',
        cardType: '',
        accessLevel: ''
    });
    const [createFormErrors, setCreateFormErrors] = useState({});
    const [createLoading, setCreateLoading] = useState(false);

    const accessLevels = [
        { label: 'VIP', value: 'vip' },
        { label: 'Standard', value: 'standard' },
        { label: 'Staff', value: 'staff' },
        { label: 'Media', value: 'media' },
        { label: 'Volunteer', value: 'volunteer' },
        { label: 'Security', value: 'security' },
        { label: 'Facilitator', value: 'facilitator' },
        { label: 'Instructor', value: 'instructor' }
    ];

    const handleConfirm = () => {
        if (selectedGroups.length > 0) {
            onConfirm(selectedGroups);
            setSelectedGroups([]);
        }
    };

    const handleCancel = () => {
        setSelectedGroups([]);
        setShowCreateForm(false);
        setCreateFormData({ title: '', description: '', cardType: '', accessLevel: '' });
        setCreateFormErrors({});
        onCancel();
    };

    const validateCreateForm = () => {
        const errors = {};

        if (!createFormData.title.trim()) {
            errors.title = 'Group name is required';
        }

        if (!createFormData.description.trim()) {
            errors.description = 'Description is required';
        }

        if (!createFormData.cardType) {
            errors.cardType = 'Card type is required';
        }

        if (!createFormData.accessLevel) {
            errors.accessLevel = 'Access level is required';
        }

        setCreateFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleCreateGroup = async () => {
        if (!validateCreateForm()) return;

        setCreateLoading(true);
        try {
            const newGroup = await onCreateGroup(createFormData);
            if (newGroup) {
                // Add the newly created group to selected groups
                setSelectedGroups(prev => [...prev, newGroup]);
                setShowCreateForm(false);
                setCreateFormData({ title: '', description: '', cardType: '', accessLevel: '' });
                setCreateFormErrors({});
            }
        } catch (error) {
            console.error('Error creating group:', error);
        } finally {
            setCreateLoading(false);
        }
    };

    return (
        <div className="space-y-4">
            {!showCreateForm ? (
                <>
                    <div className="field">
                        <div className="flex items-center justify-between mb-2">
                            <label htmlFor="groupSelection" className="block text-sm font-medium text-gray-700">
                                Available Groups
                            </label>
                            <Button
                                icon={<FiPlus size={14} />}
                                label="Create New Group"
                                className="p-button-sm p-button-outlined"
                                style={{
                                    backgroundColor: 'white',
                                    color: 'black',
                                    border: '1px solid #d1d5db',
                                    padding: '6px 12px',
                                    borderRadius: '6px',
                                    fontSize: '12px'
                                }}
                                onClick={() => setShowCreateForm(true)}
                                disabled={loading}
                            />
                        </div>
                        <MultiSelect
                            id="groupSelection"
                            value={selectedGroups}
                            options={availableGroups}
                            onChange={(e) => setSelectedGroups(e.value)}
                            optionLabel="title"
                            dataKey="id"
                            placeholder={loading
                                ? "Loading groups..."
                                : availableGroups.length === 0
                                    ? "No groups available"
                                    : "Select groups to add"
                            }
                            filter
                            display="chip"
                            className="w-full"
                            disabled={loading}
                            showClear
                        />
                        {availableGroups.length === 0 && !loading && (
                            <small className="text-gray-500 mt-1">
                                No groups available. Create a new group to get started.
                            </small>
                        )}
                    </div>

                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            label="Cancel"
                            className="p-button-outlined"
                            style={{
                                backgroundColor: 'white',
                                color: 'black',
                                border: '1px solid #d1d5db',
                                padding: '10px 16px',
                                borderRadius: '6px',
                                minHeight: '44px'
                            }}
                            onClick={handleCancel}
                            disabled={loading}
                        />
                        <Button
                            label="Add Groups"
                            style={{
                                backgroundColor: '#00c3ac',
                                color: 'white',
                                border: '1px solid #00c3ac',
                                padding: '10px 16px',
                                borderRadius: '6px',
                                minHeight: '44px'
                            }}
                            onClick={handleConfirm}
                            disabled={loading || selectedGroups.length === 0}
                            loading={loading}
                        />
                    </div>
                </>
            ) : (
                <CreateGroupForm
                    formData={createFormData}
                    setFormData={setCreateFormData}
                    errors={createFormErrors}
                    eventCardTypes={eventCardTypes}
                    accessLevels={accessLevels}
                    loading={createLoading}
                    onSubmit={handleCreateGroup}
                    onCancel={() => setShowCreateForm(false)}
                    isMobile={isMobile}
                />
            )}
        </div>
    );
};

// Create Group Form Component
const CreateGroupForm = ({ formData, setFormData, errors, eventCardTypes, accessLevels, loading, onSubmit, onCancel, isMobile }) => {
    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Create New Event Group</h3>
                <p className="text-sm text-gray-600">Create a new group specifically for this event</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
                {/* Group Name */}
                <div className="field">
                    <label htmlFor="groupTitle" className="block text-sm font-medium text-gray-700 mb-1">
                        Group Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="groupTitle"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="Enter group name"
                        className={`w-full ${errors.title ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.title && <small className="p-error">{errors.title}</small>}
                </div>

                {/* Description */}
                <div className="field">
                    <label htmlFor="groupDescription" className="block text-sm font-medium text-gray-700 mb-1">
                        Description <span className="text-red-500">*</span>
                    </label>
                    <InputTextarea
                        id="groupDescription"
                        value={formData.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="Enter group description"
                        rows={3}
                        className={`w-full ${errors.description ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.description && <small className="p-error">{errors.description}</small>}
                </div>

                {/* Card Type */}
                <div className="field">
                    <label htmlFor="cardType" className="block text-sm font-medium text-gray-700 mb-1">
                        Card Type <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="cardType"
                        value={formData.cardType}
                        options={eventCardTypes}
                        onChange={(e) => handleInputChange('cardType', e.value)}
                        optionLabel="name"
                        optionValue="name"
                        placeholder="Select card type"
                        className={`w-full ${errors.cardType ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.cardType && <small className="p-error">{errors.cardType}</small>}
                </div>

                {/* Access Level */}
                <div className="field">
                    <label htmlFor="accessLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        Access Level <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="accessLevel"
                        value={formData.accessLevel}
                        options={accessLevels}
                        onChange={(e) => handleInputChange('accessLevel', e.value)}
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        className={`w-full ${errors.accessLevel ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.accessLevel && <small className="p-error">{errors.accessLevel}</small>}
                </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onCancel}
                    disabled={loading}
                />
                <Button
                    label="Create Group"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onSubmit}
                    disabled={loading}
                    loading={loading}
                />
            </div>
        </div>
    );
};

// Member Selection Content Component
const MemberSelectionContent = ({ availableMembers, loading, onConfirm, onCancel, onCreateMember, isMobile }) => {
    const [selectedMembers, setSelectedMembers] = useState([]);
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [createFormData, setCreateFormData] = useState({
        name: '',
        email: '',
        department: '',
        role: '',
        accessLevel: ''
    });
    const [createFormErrors, setCreateFormErrors] = useState({});
    const [createLoading, setCreateLoading] = useState(false);

    const accessLevels = [
        { label: 'VIP', value: 'vip' },
        { label: 'Standard', value: 'standard' },
        { label: 'Staff', value: 'staff' },
        { label: 'Media', value: 'media' },
        { label: 'Volunteer', value: 'volunteer' },
        { label: 'Security', value: 'security' },
        { label: 'Facilitator', value: 'facilitator' },
        { label: 'Instructor', value: 'instructor' }
    ];

    const handleConfirm = () => {
        if (selectedMembers.length > 0) {
            onConfirm(selectedMembers);
            setSelectedMembers([]);
        }
    };

    const handleCancel = () => {
        setSelectedMembers([]);
        setShowCreateForm(false);
        setCreateFormData({ name: '', email: '', department: '', role: '', accessLevel: '' });
        setCreateFormErrors({});
        onCancel();
    };

    const validateCreateForm = () => {
        const errors = {};

        if (!createFormData.name.trim()) {
            errors.name = 'Name is required';
        }

        if (!createFormData.email.trim()) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(createFormData.email)) {
            errors.email = 'Email is invalid';
        }

        if (!createFormData.department.trim()) {
            errors.department = 'Department is required';
        }

        if (!createFormData.role.trim()) {
            errors.role = 'Role is required';
        }

        if (!createFormData.accessLevel) {
            errors.accessLevel = 'Access level is required';
        }

        setCreateFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleCreateMember = async () => {
        if (!validateCreateForm()) return;

        setCreateLoading(true);
        try {
            const newMember = await onCreateMember(createFormData);
            if (newMember) {
                // Add the newly created member to selected members
                setSelectedMembers(prev => [...prev, newMember]);
                setShowCreateForm(false);
                setCreateFormData({ name: '', email: '', department: '', role: '', accessLevel: '' });
                setCreateFormErrors({});
            }
        } catch (error) {
            console.error('Error creating member:', error);
        } finally {
            setCreateLoading(false);
        }
    };

    return (
        <div className="space-y-4">
            {!showCreateForm ? (
                <>
                    <div className="field">
                        <div className="flex items-center justify-between mb-2">
                            <label htmlFor="memberSelection" className="block text-sm font-medium text-gray-700">
                                Available Members
                            </label>
                            <Button
                                icon={<FiPlus size={14} />}
                                label="Create New Member"
                                className="p-button-sm p-button-outlined"
                                style={{
                                    backgroundColor: 'white',
                                    color: 'black',
                                    border: '1px solid #d1d5db',
                                    padding: '6px 12px',
                                    borderRadius: '6px',
                                    fontSize: '12px'
                                }}
                                onClick={() => setShowCreateForm(true)}
                                disabled={loading}
                            />
                        </div>
                        <MultiSelect
                            id="memberSelection"
                            value={selectedMembers}
                            options={availableMembers}
                            onChange={(e) => setSelectedMembers(e.value)}
                            optionLabel="name"
                            dataKey="id"
                            placeholder={loading
                                ? "Loading members..."
                                : availableMembers.length === 0
                                    ? "No members available"
                                    : "Select members to add"
                            }
                            filter
                            display="chip"
                            className="w-full"
                            disabled={loading}
                            showClear
                            itemTemplate={(option) => (
                                <div className="flex items-center gap-2 p-2">
                                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                        <span className="text-white text-sm font-medium">
                                            {option.name?.charAt(0)?.toUpperCase()}
                                        </span>
                                    </div>
                                    <div>
                                        <div className="font-medium">{option.name}</div>
                                        <div className="text-sm text-gray-500">{option.email}</div>
                                    </div>
                                </div>
                            )}
                        />
                        {availableMembers.length === 0 && !loading && (
                            <small className="text-gray-500 mt-1">
                                No members available. Create a new member to get started.
                            </small>
                        )}
                    </div>

                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            label="Cancel"
                            className="p-button-outlined"
                            style={{
                                backgroundColor: 'white',
                                color: 'black',
                                border: '1px solid #d1d5db',
                                padding: '10px 16px',
                                borderRadius: '6px',
                                minHeight: '44px'
                            }}
                            onClick={handleCancel}
                            disabled={loading}
                        />
                        <Button
                            label="Add Members"
                            style={{
                                backgroundColor: '#00c3ac',
                                color: 'white',
                                border: '1px solid #00c3ac',
                                padding: '10px 16px',
                                borderRadius: '6px',
                                minHeight: '44px'
                            }}
                            onClick={handleConfirm}
                            disabled={loading || selectedMembers.length === 0}
                            loading={loading}
                        />
                    </div>
                </>
            ) : (
                <CreateMemberForm
                    formData={createFormData}
                    setFormData={setCreateFormData}
                    errors={createFormErrors}
                    accessLevels={accessLevels}
                    loading={createLoading}
                    onSubmit={handleCreateMember}
                    onCancel={() => setShowCreateForm(false)}
                    isMobile={isMobile}
                />
            )}
        </div>
    );
};

// Create Member Form Component
const CreateMemberForm = ({ formData, setFormData, errors, accessLevels, loading, onSubmit, onCancel, isMobile }) => {
    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    return (
        <div className="space-y-4">
            <div className="text-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Create New Event Member</h3>
                <p className="text-sm text-gray-600">Add a new member specifically for this event</p>
            </div>

            <div className="grid grid-cols-1 gap-4">
                {/* Name */}
                <div className="field">
                    <label htmlFor="memberName" className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberName"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter full name"
                        className={`w-full ${errors.name ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.name && <small className="p-error">{errors.name}</small>}
                </div>

                {/* Email */}
                <div className="field">
                    <label htmlFor="memberEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberEmail"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Enter email address"
                        type="email"
                        className={`w-full ${errors.email ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.email && <small className="p-error">{errors.email}</small>}
                </div>

                {/* Department */}
                <div className="field">
                    <label htmlFor="memberDepartment" className="block text-sm font-medium text-gray-700 mb-1">
                        Department <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberDepartment"
                        value={formData.department}
                        onChange={(e) => handleInputChange('department', e.target.value)}
                        placeholder="Enter department"
                        className={`w-full ${errors.department ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.department && <small className="p-error">{errors.department}</small>}
                </div>

                {/* Role */}
                <div className="field">
                    <label htmlFor="memberRole" className="block text-sm font-medium text-gray-700 mb-1">
                        Role <span className="text-red-500">*</span>
                    </label>
                    <InputText
                        id="memberRole"
                        value={formData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        placeholder="Enter role/position"
                        className={`w-full ${errors.role ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.role && <small className="p-error">{errors.role}</small>}
                </div>

                {/* Access Level */}
                <div className="field">
                    <label htmlFor="memberAccessLevel" className="block text-sm font-medium text-gray-700 mb-1">
                        Access Level <span className="text-red-500">*</span>
                    </label>
                    <Dropdown
                        id="memberAccessLevel"
                        value={formData.accessLevel}
                        options={accessLevels}
                        onChange={(e) => handleInputChange('accessLevel', e.value)}
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Select access level"
                        className={`w-full ${errors.accessLevel ? 'p-invalid' : ''}`}
                        disabled={loading}
                    />
                    {errors.accessLevel && <small className="p-error">{errors.accessLevel}</small>}
                </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onCancel}
                    disabled={loading}
                />
                <Button
                    label="Create Member"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px'
                    }}
                    onClick={onSubmit}
                    disabled={loading}
                    loading={loading}
                />
            </div>
        </div>
    );
};

export default AssociatedGroupsModal;
