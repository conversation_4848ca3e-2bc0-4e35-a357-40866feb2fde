import React, { useState, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { MultiSelect } from 'primereact/multiselect';
import { Toast } from 'primereact/toast';
import { motion } from 'framer-motion';
import { FiUsers, FiInfo, FiPlus, FiUserPlus } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';
import axiosInstance from '../../../../config/Axios';
import {
    mockAvailableEventGroups,
    mockAvailableEventMembers,
    getAvailableEventGroupsForEvent,
    getAvailableEventMembersForEvent
} from '@data/mockEventGroupsData';

const AssociatedGroupsModal = ({ visible, onHide, event, onUpdateEvent }) => {
    const { isMobile } = useLayout();

    // State for modals and data
    const [showAddGroupsModal, setShowAddGroupsModal] = useState(false);
    const [showAddMembersModal, setShowAddMembersModal] = useState(false);
    const [selectedGroupForMembers, setSelectedGroupForMembers] = useState(null);
    const [availableGroups, setAvailableGroups] = useState([]);
    const [availableMembers, setAvailableMembers] = useState([]);
    const [loading, setLoading] = useState(false);

    if (!event) return null;

    const groups = event.associatedGroups || [];

    // Fetch available event groups for selection
    const fetchAvailableEventGroups = async () => {
        try {
            setLoading(true);
            // Use event-specific endpoint to get available event groups
            const response = await axiosInstance.get(`/events/${event.id}/available-groups`);
            const allEventGroups = Array.isArray(response.data) ? response.data : (response.data.data || []);

            // Filter out groups that are already associated with the event
            const associatedGroupIds = groups.map(g => g.id);
            const filteredGroups = allEventGroups.filter(group => !associatedGroupIds.includes(group.id));

            setAvailableGroups(filteredGroups);
        } catch (error) {
            console.error('Error fetching event groups:', error);
            // Fallback: use mock data for development
            const mockGroups = getAvailableEventGroupsForEvent(event.id);
            const associatedGroupIds = groups.map(g => g.id);
            const filteredMockGroups = mockGroups.filter(group => !associatedGroupIds.includes(group.id));
            setAvailableGroups(filteredMockGroups);
        } finally {
            setLoading(false);
        }
    };

    // Fetch available members for event group selection
    const fetchAvailableMembers = async () => {
        try {
            setLoading(true);
            // Use event-specific endpoint to get members available for event groups
            const response = await axiosInstance.get(`/events/${event.id}/available-members`);
            const allMembers = Array.isArray(response.data) ? response.data : (response.data.data || []);
            setAvailableMembers(allMembers);
        } catch (error) {
            console.error('Error fetching event members:', error);
            // Fallback: try general users endpoint if event-specific doesn't exist yet
            try {
                const fallbackResponse = await axiosInstance.get('/users');
                const fallbackMembers = Array.isArray(fallbackResponse.data) ? fallbackResponse.data : (fallbackResponse.data.data || []);
                setAvailableMembers(fallbackMembers);
            } catch (fallbackError) {
                console.error('Error fetching fallback members:', fallbackError);
                setAvailableMembers([]);
            }
        } finally {
            setLoading(false);
        }
    };

    // Handle adding event groups to event
    const handleAddEventGroupsToEvent = async (selectedGroups) => {
        try {
            setLoading(true);

            // Use event-specific API to associate groups with the event
            const payload = {
                event_id: event.id,
                group_ids: selectedGroups.map(group => group.id)
            };

            await axiosInstance.post(`/events/${event.id}/groups`, payload);

            // Update the local event data
            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, ...selectedGroups]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddGroupsModal(false);
        } catch (error) {
            console.error('Error adding event groups to event:', error);
            // For development, still update locally even if API fails
            const updatedEvent = {
                ...event,
                associatedGroups: [...groups, ...selectedGroups]
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddGroupsModal(false);
        } finally {
            setLoading(false);
        }
    };

    // Handle adding members to a specific event group
    const handleAddMembersToEventGroup = async (selectedMembers) => {
        try {
            setLoading(true);

            if (!selectedGroupForMembers) return;

            const payload = {
                event_id: event.id,
                group_id: selectedGroupForMembers.id,
                user_ids: selectedMembers.map(member => member.id)
            };

            // Call event-specific API to add members to event group
            await axiosInstance.post(`/event-groups/${selectedGroupForMembers.id}/members`, payload);

            // Update the group's member count in the local state
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + selectedMembers.length }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddMembersModal(false);
            setSelectedGroupForMembers(null);
        } catch (error) {
            console.error('Error adding members to event group:', error);
            // For development, still update locally even if API fails
            const updatedGroups = groups.map(group =>
                group.id === selectedGroupForMembers.id
                    ? { ...group, memberCount: group.memberCount + selectedMembers.length }
                    : group
            );

            const updatedEvent = {
                ...event,
                associatedGroups: updatedGroups
            };

            if (onUpdateEvent) {
                onUpdateEvent(updatedEvent);
            }

            setShowAddMembersModal(false);
            setSelectedGroupForMembers(null);
        } finally {
            setLoading(false);
        }
    };

    // Template functions for DataTable columns
    const groupNameTemplate = (rowData) => (
        <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white font-medium">
                    {rowData.title?.charAt(0)?.toUpperCase()}
                </span>
            </div>
            <div>
                <div className="font-medium">{rowData.title}</div>
                <div className="text-sm text-gray-500">{rowData.description}</div>
            </div>
        </div>
    );

    const cardTypeTemplate = (rowData) => (
        <Tag
            value={rowData.card_type_name}
            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
        />
    );

    const statusTemplate = (rowData) => {
        const statusColors = {
            active: '#22C55E',
            inactive: '#6B7280',
            pending: '#F59E0B'
        };
        
        return (
            <Tag
                value={rowData.status}
                style={{ backgroundColor: statusColors[rowData.status] || '#6B7280' }}
                className="text-white px-3 py-1 rounded-full text-sm capitalize"
            />
        );
    };

    const memberCountTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiUsers size={14} className="text-gray-500" />
            <span className="text-sm font-medium">
                {rowData.memberCount} {rowData.memberCount === 1 ? 'member' : 'members'}
            </span>
        </div>
    );

    const actionsTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <Button
                icon={<FiUserPlus size={14} />}
                className="p-button-sm p-button-outlined"
                style={{
                    backgroundColor: 'white',
                    color: 'black',
                    border: '1px solid #d1d5db',
                    padding: '6px 12px',
                    borderRadius: '6px',
                    transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-1px)';
                    e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = 'none';
                }}
                onClick={() => {
                    setSelectedGroupForMembers(rowData);
                    fetchAvailableMembers();
                    setShowAddMembersModal(true);
                }}
                tooltip="Add Members"
                tooltipOptions={{ position: 'top' }}
            />
        </div>
    );

    // Mobile view for groups
    const MobileGroupsList = () => (
        <div className="space-y-4">
            {groups.map((group) => (
                <motion.div
                    key={group.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border border-gray-200 rounded-lg p-4 bg-white"
                >
                    <div className="flex items-start gap-3 mb-3">
                        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                                {group.title?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                        <div className="flex-1">
                            <div className="flex items-start justify-between mb-1">
                                <h3 className="font-semibold text-lg text-gray-900">{group.title}</h3>
                                <Button
                                    icon={<FiUserPlus size={16} />}
                                    className="p-button-sm p-button-outlined"
                                    style={{
                                        backgroundColor: 'white',
                                        color: 'black',
                                        border: '1px solid #d1d5db',
                                        padding: '8px 10px',
                                        borderRadius: '6px',
                                        minWidth: 'auto',
                                        minHeight: '44px', // Better touch target for mobile
                                        transition: 'all 0.2s ease'
                                    }}
                                    onClick={() => {
                                        setSelectedGroupForMembers(group);
                                        fetchAvailableMembers();
                                        setShowAddMembersModal(true);
                                    }}
                                    tooltip="Add Members"
                                    tooltipOptions={{ position: 'top' }}
                                />
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                            <div className="flex items-center gap-2 mb-2">
                                <Tag
                                    value={group.card_type_name}
                                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                                />
                                <Tag
                                    value={group.status}
                                    style={{
                                        backgroundColor: group.status === 'active' ? '#22C55E' : '#6B7280'
                                    }}
                                    className="text-white px-2 py-1 rounded-full text-xs capitalize"
                                />
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiUsers size={14} />
                                <span>{group.memberCount} {group.memberCount === 1 ? 'member' : 'members'}</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
            ))}
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2">
                        <FiUsers className="text-blue-600" size={20} />
                        <span>Associated Groups - {event.name}</span>
                    </div>
                    <Button
                        icon={<FiPlus size={16} />}
                        label={isMobile ? "" : "Add Groups"}
                        className="p-button-sm"
                        style={{
                            backgroundColor: 'white',
                            color: 'black',
                            border: '1px solid #d1d5db',
                            padding: isMobile ? '8px' : '6px 12px',
                            borderRadius: '6px',
                            transition: 'all 0.2s ease',
                            minHeight: '44px', // Better touch target for mobile
                            minWidth: isMobile ? '44px' : 'auto'
                        }}
                        onMouseEnter={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(-1px)';
                                e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isMobile) {
                                e.target.style.transform = 'translateY(0)';
                                e.target.style.boxShadow = 'none';
                            }
                        }}
                        onClick={() => {
                            fetchAvailableEventGroups();
                            setShowAddGroupsModal(true);
                        }}
                        tooltip="Add Groups to Event"
                        tooltipOptions={{ position: 'bottom' }}
                    />
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '70vw',
                maxWidth: isMobile ? '95vw' : '1000px',
                height: isMobile ? '90vh' : 'auto',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '95vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="associated-groups-modal"
            contentStyle={{
                height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
        >
            <div className="space-y-4">
                {/* Event Info */}
                <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                    <div className="flex items-center gap-2 mb-2">
                        <FiInfo className="text-blue-600" size={16} />
                        <span className="font-medium text-blue-900">Event Information</span>
                    </div>
                    <div className="text-sm text-blue-800">
                        <p><strong>Event:</strong> {event.name}</p>
                        <p><strong>Location:</strong> {event.location}</p>
                        <p><strong>Date:</strong> {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}</p>
                        <p><strong>Total Groups:</strong> {groups.length}</p>
                    </div>
                </motion.div>

                {/* Groups Content */}
                {groups.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="text-center py-8"
                    >
                        <FiUsers size={48} className="text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No Associated Groups</h3>
                        <p className="text-gray-400">This event has no groups associated with it yet.</p>
                    </motion.div>
                ) : (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        {isMobile ? (
                            <MobileGroupsList />
                        ) : (
                            <DataTable
                                value={groups}
                                className="border border-gray-200 rounded-lg"
                                emptyMessage="No groups found"
                                responsiveLayout="stack"
                                breakpoint="960px"
                            >
                                <Column
                                    body={groupNameTemplate}
                                    header="Group Name"
                                    style={{ minWidth: '250px' }}
                                />
                                <Column
                                    body={cardTypeTemplate}
                                    header="Card Type"
                                    style={{ minWidth: '150px' }}
                                />
                                <Column
                                    body={statusTemplate}
                                    header="Status"
                                    style={{ minWidth: '100px' }}
                                />
                                <Column
                                    body={memberCountTemplate}
                                    header="Members"
                                    style={{ minWidth: '120px' }}
                                />
                                <Column
                                    body={actionsTemplate}
                                    header="Actions"
                                    style={{ minWidth: '100px' }}
                                    exportable={false}
                                />
                            </DataTable>
                        )}
                    </motion.div>
                )}
            </div>

            {/* Group Selection Modal */}
            <Dialog
                header="Add Groups to Event"
                visible={showAddGroupsModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => setShowAddGroupsModal(false)}
                className="add-groups-modal"
                modal
            >
                <div className="space-y-4">
                    <div className="text-sm text-gray-600 mb-4">
                        Select groups to associate with <strong>{event.name}</strong>
                    </div>

                    <GroupSelectionContent
                        availableGroups={availableGroups}
                        loading={loading}
                        onConfirm={handleAddEventGroupsToEvent}
                        onCancel={() => setShowAddGroupsModal(false)}
                        isMobile={isMobile}
                    />
                </div>
            </Dialog>

            {/* Member Selection Modal */}
            <Dialog
                header={`Add Members to ${selectedGroupForMembers?.title || 'Group'}`}
                visible={showAddMembersModal}
                style={{
                    width: isMobile ? '95vw' : '50vw',
                    maxWidth: isMobile ? '95vw' : '600px'
                }}
                breakpoints={{
                    '960px': '80vw',
                    '641px': '95vw'
                }}
                onHide={() => {
                    setShowAddMembersModal(false);
                    setSelectedGroupForMembers(null);
                }}
                className="add-members-modal"
                modal
            >
                <div className="space-y-4">
                    <div className="text-sm text-gray-600 mb-4">
                        Select members to add to <strong>{selectedGroupForMembers?.title}</strong>
                    </div>

                    <MemberSelectionContent
                        availableMembers={availableMembers}
                        loading={loading}
                        onConfirm={handleAddMembersToEventGroup}
                        onCancel={() => {
                            setShowAddMembersModal(false);
                            setSelectedGroupForMembers(null);
                        }}
                        isMobile={isMobile}
                    />
                </div>
            </Dialog>
        </Dialog>
    );
};

// Group Selection Content Component
const GroupSelectionContent = ({ availableGroups, loading, onConfirm, onCancel, isMobile }) => {
    const [selectedGroups, setSelectedGroups] = useState([]);

    const handleConfirm = () => {
        if (selectedGroups.length > 0) {
            onConfirm(selectedGroups);
            setSelectedGroups([]);
        }
    };

    const handleCancel = () => {
        setSelectedGroups([]);
        onCancel();
    };

    return (
        <div className="space-y-4">
            <div className="field">
                <label htmlFor="groupSelection" className="block text-sm font-medium text-gray-700 mb-2">
                    Available Groups
                </label>
                <MultiSelect
                    id="groupSelection"
                    value={selectedGroups}
                    options={availableGroups}
                    onChange={(e) => setSelectedGroups(e.value)}
                    optionLabel="title"
                    dataKey="id"
                    placeholder={loading
                        ? "Loading groups..."
                        : availableGroups.length === 0
                            ? "No groups available"
                            : "Select groups to add"
                    }
                    filter
                    display="chip"
                    className="w-full"
                    disabled={loading || availableGroups.length === 0}
                    showClear
                />
                {availableGroups.length === 0 && !loading && (
                    <small className="text-gray-500 mt-1">
                        All available groups are already associated with this event.
                    </small>
                )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px' // Better touch target for mobile
                    }}
                    onClick={handleCancel}
                    disabled={loading}
                />
                <Button
                    label="Add Groups"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px' // Better touch target for mobile
                    }}
                    onClick={handleConfirm}
                    disabled={loading || selectedGroups.length === 0}
                    loading={loading}
                />
            </div>
        </div>
    );
};

// Member Selection Content Component
const MemberSelectionContent = ({ availableMembers, loading, onConfirm, onCancel, isMobile }) => {
    const [selectedMembers, setSelectedMembers] = useState([]);

    const handleConfirm = () => {
        if (selectedMembers.length > 0) {
            onConfirm(selectedMembers);
            setSelectedMembers([]);
        }
    };

    const handleCancel = () => {
        setSelectedMembers([]);
        onCancel();
    };

    return (
        <div className="space-y-4">
            <div className="field">
                <label htmlFor="memberSelection" className="block text-sm font-medium text-gray-700 mb-2">
                    Available Members
                </label>
                <MultiSelect
                    id="memberSelection"
                    value={selectedMembers}
                    options={availableMembers}
                    onChange={(e) => setSelectedMembers(e.value)}
                    optionLabel="name"
                    dataKey="id"
                    placeholder={loading
                        ? "Loading members..."
                        : availableMembers.length === 0
                            ? "No members available"
                            : "Select members to add"
                    }
                    filter
                    display="chip"
                    className="w-full"
                    disabled={loading || availableMembers.length === 0}
                    showClear
                    itemTemplate={(option) => (
                        <div className="flex items-center gap-2 p-2">
                            <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                                <span className="text-white text-sm font-medium">
                                    {option.name?.charAt(0)?.toUpperCase()}
                                </span>
                            </div>
                            <div>
                                <div className="font-medium">{option.name}</div>
                                <div className="text-sm text-gray-500">{option.email}</div>
                            </div>
                        </div>
                    )}
                />
                {availableMembers.length === 0 && !loading && (
                    <small className="text-gray-500 mt-1">
                        No members available to add.
                    </small>
                )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
                <Button
                    label="Cancel"
                    className="p-button-outlined"
                    style={{
                        backgroundColor: 'white',
                        color: 'black',
                        border: '1px solid #d1d5db',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px' // Better touch target for mobile
                    }}
                    onClick={handleCancel}
                    disabled={loading}
                />
                <Button
                    label="Add Members"
                    style={{
                        backgroundColor: '#00c3ac',
                        color: 'white',
                        border: '1px solid #00c3ac',
                        padding: '10px 16px',
                        borderRadius: '6px',
                        minHeight: '44px' // Better touch target for mobile
                    }}
                    onClick={handleConfirm}
                    disabled={loading || selectedMembers.length === 0}
                    loading={loading}
                />
            </div>
        </div>
    );
};

export default AssociatedGroupsModal;
