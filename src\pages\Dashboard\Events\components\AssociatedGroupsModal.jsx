import React from 'react';
import { Dialog } from 'primereact/dialog';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Tag } from 'primereact/tag';
import { motion } from 'framer-motion';
import { FiUsers, FiInfo } from 'react-icons/fi';
import { useLayout } from '@contexts/LayoutContext';

const AssociatedGroupsModal = ({ visible, onHide, event }) => {
    const { isMobile } = useLayout();

    if (!event) return null;

    const groups = event.associatedGroups || [];

    // Template functions for DataTable columns
    const groupNameTemplate = (rowData) => (
        <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white font-medium">
                    {rowData.title?.charAt(0)?.toUpperCase()}
                </span>
            </div>
            <div>
                <div className="font-medium">{rowData.title}</div>
                <div className="text-sm text-gray-500">{rowData.description}</div>
            </div>
        </div>
    );

    const cardTypeTemplate = (rowData) => (
        <Tag
            value={rowData.card_type_name}
            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
        />
    );

    const statusTemplate = (rowData) => {
        const statusColors = {
            active: '#22C55E',
            inactive: '#6B7280',
            pending: '#F59E0B'
        };
        
        return (
            <Tag
                value={rowData.status}
                style={{ backgroundColor: statusColors[rowData.status] || '#6B7280' }}
                className="text-white px-3 py-1 rounded-full text-sm capitalize"
            />
        );
    };

    const memberCountTemplate = (rowData) => (
        <div className="flex items-center gap-2">
            <FiUsers size={14} className="text-gray-500" />
            <span className="text-sm font-medium">
                {rowData.memberCount} {rowData.memberCount === 1 ? 'member' : 'members'}
            </span>
        </div>
    );

    // Mobile view for groups
    const MobileGroupsList = () => (
        <div className="space-y-4">
            {groups.map((group) => (
                <motion.div
                    key={group.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border border-gray-200 rounded-lg p-4 bg-white"
                >
                    <div className="flex items-start gap-3 mb-3">
                        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center">
                            <span className="text-white font-bold text-lg">
                                {group.title?.charAt(0)?.toUpperCase()}
                            </span>
                        </div>
                        <div className="flex-1">
                            <h3 className="font-semibold text-lg text-gray-900 mb-1">{group.title}</h3>
                            <p className="text-sm text-gray-600 mb-2">{group.description}</p>
                            <div className="flex items-center gap-2 mb-2">
                                <Tag
                                    value={group.card_type_name}
                                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs"
                                />
                                <Tag
                                    value={group.status}
                                    style={{ 
                                        backgroundColor: group.status === 'active' ? '#22C55E' : '#6B7280' 
                                    }}
                                    className="text-white px-2 py-1 rounded-full text-xs capitalize"
                                />
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiUsers size={14} />
                                <span>{group.memberCount} {group.memberCount === 1 ? 'member' : 'members'}</span>
                            </div>
                        </div>
                    </div>
                </motion.div>
            ))}
        </div>
    );

    return (
        <Dialog
            header={
                <div className="flex items-center gap-2">
                    <FiUsers className="text-blue-600" size={20} />
                    <span>Associated Groups - {event.name}</span>
                </div>
            }
            visible={visible}
            style={{
                width: isMobile ? '95vw' : '70vw',
                maxWidth: isMobile ? '95vw' : '1000px',
                height: isMobile ? '90vh' : 'auto',
                maxHeight: '90vh'
            }}
            breakpoints={{
                '960px': '95vw',
                '641px': '95vw'
            }}
            onHide={onHide}
            className="associated-groups-modal"
            contentStyle={{
                height: isMobile ? 'calc(90vh - 60px)' : 'auto',
                overflow: 'auto',
                padding: isMobile ? '15px' : '20px'
            }}
        >
            <div className="space-y-4">
                {/* Event Info */}
                <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="bg-blue-50 p-4 rounded-lg border border-blue-200"
                >
                    <div className="flex items-center gap-2 mb-2">
                        <FiInfo className="text-blue-600" size={16} />
                        <span className="font-medium text-blue-900">Event Information</span>
                    </div>
                    <div className="text-sm text-blue-800">
                        <p><strong>Event:</strong> {event.name}</p>
                        <p><strong>Location:</strong> {event.location}</p>
                        <p><strong>Date:</strong> {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}</p>
                        <p><strong>Total Groups:</strong> {groups.length}</p>
                    </div>
                </motion.div>

                {/* Groups Content */}
                {groups.length === 0 ? (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                        className="text-center py-8"
                    >
                        <FiUsers size={48} className="text-gray-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-gray-500 mb-2">No Associated Groups</h3>
                        <p className="text-gray-400">This event has no groups associated with it yet.</p>
                    </motion.div>
                ) : (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: 0.2 }}
                    >
                        {isMobile ? (
                            <MobileGroupsList />
                        ) : (
                            <DataTable
                                value={groups}
                                className="border border-gray-200 rounded-lg"
                                emptyMessage="No groups found"
                                responsiveLayout="stack"
                                breakpoint="960px"
                            >
                                <Column 
                                    body={groupNameTemplate} 
                                    header="Group Name" 
                                    style={{ minWidth: '250px' }}
                                />
                                <Column 
                                    body={cardTypeTemplate} 
                                    header="Card Type" 
                                    style={{ minWidth: '150px' }}
                                />
                                <Column 
                                    body={statusTemplate} 
                                    header="Status" 
                                    style={{ minWidth: '100px' }}
                                />
                                <Column 
                                    body={memberCountTemplate} 
                                    header="Members" 
                                    style={{ minWidth: '120px' }}
                                />
                            </DataTable>
                        )}
                    </motion.div>
                )}
            </div>
        </Dialog>
    );
};

export default AssociatedGroupsModal;
