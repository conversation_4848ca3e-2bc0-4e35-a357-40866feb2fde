
import { useEffect, useState, useCallback, useMemo } from 'react'
import { Link } from 'react-router-dom';
import { useRef } from 'react';

import { useDeleteGroupMutation } from '@quires'
import { groupsTableConfig, defaultTableConfig } from '@constants';
import { useGroupsDataTableContext } from '@contexts/GroupsDataTableContext';
import { useLayout } from '@contexts/LayoutContext';
import { useQueryClient } from 'react-query';

import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Dialog } from 'primereact/dialog';

import { TfiTrash } from "react-icons/tfi";
import { FaRegEye } from 'react-icons/fa';
import { FaSearch } from "react-icons/fa";
import { HiDotsVertical } from 'react-icons/hi';
import { FiEdit } from 'react-icons/fi';
import { FaPalette } from 'react-icons/fa';
import { FaUsers } from 'react-icons/fa';

import { createPortal } from 'react-dom';
import GroupForm from '../../Backages/CreateGroupForm';
import Container from '@components/Container';
import axiosInstance from '../../../../config/Axios';
import { motion } from 'framer-motion';
import ImageGenerationModal from '../../DesignSpace/components/ImageGenerationModal';

// Import Avatar components
import { Avatar, AvatarFallback, AvatarImage } from '../../../../components/ui/avatar';
import React from 'react';

/* eslint-disable react/prop-types */
function GroupsDataTable() {
    const { totalRecords, lazyParams, setLazyParams, data, dataHandler, loading } = useGroupsDataTableContext();
    const { isMobile } = useLayout();

    const [isCreateGroupModalOpen, setisCreateGroupModalOpen] = useState(false);
    const [isEditGroupModalOpen, setIsEditGroupModalOpen] = useState(false);
    const [groupBeingEdited, setGroupBeingEdited] = useState(null); // State to hold the rowData for editing
    const deleteRow = useDeleteGroupMutation();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedType, setSelectedType] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('');
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);
    const toast = useRef(null);
    const [subscriptionError, setSubscriptionError] = useState(null);
    const [subscriptionLoading, setSubscriptionLoading] = useState(true);
    const [noPackage, setNoPackage] = useState(false);

    // استخراج الأنواع والحالات الفريدة
    const cardTypes = useMemo(() => {
        if (!data) return [];
        const types = data.map(g => g.card_type_name).filter(Boolean);
        return Array.from(new Set(types));
    }, [data]);
    const statuses = useMemo(() => {
        if (!data) return [];
        const sts = data.map(g => g.status || 'inactive').filter(Boolean);
        return Array.from(new Set(sts));
    }, [data]);
    // تصفية البيانات حسب البحث والفلاتر
    const filteredData = useMemo(() => {
        let result = data || [];
        if (selectedType) {
            result = result.filter(g => g.card_type_name === selectedType);
        }
        if (selectedStatus) {
            result = result.filter(g => (g.status || 'inactive') === selectedStatus);
        }
        if (searchQuery) {
            result = result.filter(g => (g.title || '').toLowerCase().includes(searchQuery.toLowerCase()));
        }
        return result;
    }, [data, selectedType, selectedStatus, searchQuery]);

    useEffect(() => {
        const checkSubscription = async () => {
            try {
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('user_id');
                if (!token || !userId) {
                    setSubscriptionError({ message: 'User not authenticated.' });
                    setSubscriptionLoading(false);
                    return;
                }
                const response = await axiosInstance.get(`packages/show-package-by-id/${userId}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                if (!response.data || Object.keys(response.data).length === 0 || response.data.error === 'No package found for this user') {
                    setNoPackage(true);
                } else {
                    setNoPackage(false);
                }
                setSubscriptionError(null);
            } catch (error) {
                if (error.response && error.response.data) {
                    const errMsg = error.response.data.error?.toLowerCase() || '';
                    if (error.response.data.error === "Your subscription has expired. Please renew your subscription to continue.") {
                        setSubscriptionError({ message: error.response.data.error });
                    } else if (
                        errMsg.includes('not found') ||
                        errMsg.includes('no package') ||
                        errMsg.includes('no active package found for this user') ||
                        errMsg.includes('must have an active package')
                    ) {
                        setNoPackage(true);
                    } else {
                        setSubscriptionError(null);
                    }
                } else {
                    setSubscriptionError(null);
                }
            } finally {
                setSubscriptionLoading(false);
            }
        };
        checkSubscription();
    }, []);

    // Add debounced search handler
    useEffect(() => {
        const timeout = setTimeout(() => {
            setLazyParams(prev => {
                const newFilters = {
                    ...prev.filters,
                    title: { value: searchQuery, matchMode: 'contains' }
                };
                
                // تجنب إعادة التحميل إذا لم تتغير الفلاتر
                if (JSON.stringify(prev.filters) === JSON.stringify(newFilters)) {
                    return prev;
                }
                
                return {
                    ...prev,
                    filters: newFilters
                };
            });
        }, 100); // تقليل التأخير من 300ms إلى 100ms
    
        return () => clearTimeout(timeout);
    }, [searchQuery, setLazyParams]);

    // تحميل البيانات الأولية
    useEffect(() => {
        console.log('🚀 Loading initial groups data');
        setLazyParams({ ...defaultTableConfig, ...groupsTableConfig });
    }, [setLazyParams]);

    const deleteAdHandler = async (rowData) => {
        await deleteRow.mutateAsync({
            id: rowData?.id,
        }, {
            onSuccess: () => {
                setLazyParams(prev => ({ ...prev, ...groupsTableConfig }));
                
                // Show success toast
                // toast.current.show({
                //     severity: 'success',
                //     summary: 'Success',
                //     detail: 'Group deleted successfully',
                //     life: 3000
                // });
            },
            onError: () => {
                // Show error toast
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to delete group',
                    life: 3000
                });
            }
        })
    }

    // --- Success Handler (called by GroupForm on successful create/update) ---
    const handleSuccess = useCallback(() => {
        console.log("GroupForm reported success. Refreshing data...");
        // Refresh data by triggering the DataTable context's handler/refetch mechanism
        // Resetting page to 0 ensures user sees the newly created/updated item if on first page
        setLazyParams(prev => ({ ...prev, page: 0, ...groupsTableConfig }));
        // Optionally clear edit state, though GroupForm closing should handle this too
        setGroupBeingEdited(null);
    }, [setLazyParams]); // Depends on setLazyParams to refresh

    // --- Handler to open the Edit Modal ---
    const handleEditClick = (groupData) => {
        console.log("Editing group, data received:", groupData);
        const editedData = {
          ...groupData,
          card_type_id: groupData.card_type_id || groupData.card_type?.id 
        };
        setGroupBeingEdited(editedData);
        setIsEditGroupModalOpen(true);
      };


    const handleDeleteClick = (rowData) => {
        console.log(rowData)
        confirmDialog({
            message: 'Are you sure you want to delete this group?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes',
            rejectLabel: 'No',
            accept: () => deleteAdHandler(rowData),
        });
    };

    // --- State for Designs Modal ---
    const [isDesignsModalOpen, setIsDesignsModalOpen] = useState(false);
    const queryClient = useQueryClient();
    const [designsLoading, setDesignsLoading] = useState(false);
    const [groupDesigns, setGroupDesigns] = useState([]);
    const [selectedDesigns, setSelectedDesigns] = useState([]);
    const [searchDesign, setSearchDesign] = useState("");
    const [saveLoading, setSaveLoading] = useState(false);
    const [saveMessage, setSaveMessage] = useState("");
    const [saveError, setSaveError] = useState("");
    const [currentGroupId, setCurrentGroupId] = useState(null);
    const [showImageGenerationModal, setShowImageGenerationModal] = useState(false);
    const [currentBatchId, setCurrentBatchId] = useState(null);
    const [currentDesignId, setCurrentDesignId] = useState(null);

    // Fetch designs and filter by group card_type_id (like GroupFilter)
    const fetchGroupDesigns = async (group) => {
        setDesignsLoading(true);
        let allDesigns = queryClient.getQueryData("getDesigns");
        try {
            if (!allDesigns || !Array.isArray(allDesigns) || allDesigns.length === 0) {
                const response = await axiosInstance.get('/designs');
                if (response.data && Array.isArray(response.data.data)) {
                    allDesigns = response.data.data;
                    queryClient.setQueryData("getDesigns", allDesigns);
                } else {
                    allDesigns = [];
                }
            }
            // filter by group card_type_id
            let filtered = allDesigns;
            if (group.card_type_id) {
                filtered = allDesigns.filter(design => {
                    if (design.card_type_id) return design.card_type_id === group.card_type_id;
                    if (design.card_type && design.card_type.id) return design.card_type.id === group.card_type_id;
                    return true;
                });
            }
            setGroupDesigns(filtered);
            return filtered; // أضف هذا السطر
        } catch (e) {
            setGroupDesigns([]);
            return [];
        } finally {
            setDesignsLoading(false);
        }
    };

    const openDesignsModal = async (group) => {
        console.log('openDesignsModal called', group);
        setIsDesignsModalOpen(true);
        setSelectedDesigns([]);
        setCurrentGroupId(group.id);
        const filteredDesigns = await fetchGroupDesigns(group);
        try {
            const response = await axiosInstance.get(`/groups-designs?group_id=${group.id}`);
            const apiData = Array.isArray(response.data.data) ? response.data.data : Array.isArray(response.data) ? response.data : [];
            if (apiData.length > 0) {
                const selectedIds = apiData.map(item => Number(item.design_id));
                const selected = filteredDesigns.filter(design => selectedIds.includes(Number(design.id)));

                setSelectedDesigns(selected);
            } else {
                setSelectedDesigns([]);
            }
        } catch (e) {
            
            setSelectedDesigns([]);
        }
    };
    const closeDesignsModal = () => {
        setIsDesignsModalOpen(false);
        setGroupDesigns([]);
        setSelectedDesigns([]);
        setCurrentGroupId(null);
        setSaveMessage("");
        setSaveError("");
        setSaveLoading(false);
    };

    const handleSaveDesigns = async () => {
        console.log('🚩 [handleSaveDesigns] بدأ حفظ التصاميم', { selectedDesigns, currentGroupId });
        if (!currentGroupId || selectedDesigns.length === 0) {
            console.log('🚩 [handleSaveDesigns] لا يوجد مجموعة أو تصاميم محددة');
            return;
        }
        setSaveLoading(true);
        setSaveMessage("");
        setSaveError("");
        try {
            // حفظ التصاميم للمجموعة
            await axiosInstance.post(`/groups/${currentGroupId}/designs`, {
                group_id: currentGroupId,
                design_ids: selectedDesigns.map(d => d.id)
            });
            console.log('🚩 [handleSaveDesigns] تم حفظ التصاميم بنجاح');

            // جلب التصاميم المرتبطة حاليًا بالمجموعة
            const oldDesignsResponse = await axiosInstance.get(`/groups-designs?group_id=${currentGroupId}`);
            const oldDesignIds = (oldDesignsResponse.data.data || []).map(d => Number(d.design_id));
            // التصاميم الجديدة فقط
            const newDesigns = selectedDesigns.filter(d => !oldDesignIds.includes(Number(d.id)));
            console.log('🚩 [handleSaveDesigns] التصاميم الجديدة فقط:', newDesigns);
            if (newDesigns.length === 0) {
                setSaveMessage("No new designs selected.");
                return;
            }
            
            // جلب المستخدمين المرتبطين في بطاقات فقط (وليس جميع المستخدمين في المجموعة)
            const usersResponse = await axiosInstance.get(`/groups/${currentGroupId}/users-with-cards`);
            const users = usersResponse.data.data || [];
            console.log('🚩 [handleSaveDesigns] المستخدمين المرتبطين في بطاقات:', users);
            
            if (users.length === 0) {
                setSaveMessage("No users with cards found in this group.");
                setTimeout(() => {
                    closeDesignsModal();
                }, 2000);
                return;
            }
            
            // إنشاء batch
            const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const batchData = {
                total_users: users.length,
                total_designs: newDesigns.length,
                total_expected_images: users.length * newDesigns.length,
                completed_images: 0,
                status: 'processing',
                user_ids: users.map(u => u.id),
                design_ids: newDesigns.map(d => d.id),
                group_id: currentGroupId,
                created_at: new Date().toISOString()
            };
            
            // تخزين في localStorage للـ frontend
            localStorage.setItem(`batch_${batchId}`, JSON.stringify(batchData));
            
            // تخزين في Cache للـ backend
            try {
                await axiosInstance.post('/cache/batch-data', {
                    batch_id: batchId,
                    batch_data: batchData
                });
            } catch (error) {
                console.warn('Failed to store batch data in cache:', error);
            }
            
            console.log('🚩 [handleSaveDesigns] batchData:', batchData);
            
            // إرسال jobs فقط للجديدة وللمستخدمين المرتبطين في بطاقات
            const jobPromises = [];
            for (const user of users) {
                for (const design of newDesigns) {
                    jobPromises.push(
                        axiosInstance.post('/jobs/dispatch-design-image', {
                            user_id: user.id,
                            design_id: design.id,
                            group_id: currentGroupId
                        })
                    );
                }
            }
            await Promise.all(jobPromises);
            console.log('🚩 [handleSaveDesigns] تم إرسال جميع jobs بنجاح');
            setSaveMessage("Image generation started for new designs and users with cards only.");
            setShowImageGenerationModal(true);
            setCurrentBatchId(batchId);
            setCurrentDesignId(newDesigns[0].id);
            setTimeout(() => {
                closeDesignsModal();
            }, 2000);
        } catch (error) {
            console.error("🚩 [handleSaveDesigns] Error saving designs:", error);
            setSaveError("Failed to save designs. Please try again.");
        } finally {
            setSaveLoading(false);
        }
    };

    // طباعة عند إظهار المودال
    useEffect(() => {
        if (showImageGenerationModal) {
            console.log('🚩 [Modal] showImageGenerationModal:', showImageGenerationModal, 'batchId:', currentBatchId, 'designId:', currentDesignId);
        }
    }, [showImageGenerationModal, currentBatchId, currentDesignId]);

    // Filtered designs for search
    const filteredDesigns = groupDesigns.filter(design => {
        const name = design.title || design.name || "";
        return name.toLowerCase().includes(searchDesign.toLowerCase());
    });

    // Mobile action menu component
    /**
     * @param {{ group: any, isOpen: boolean, onClose: function }} props
     */
    const MobileActionMenu = ({ group, isOpen, onClose }) => {
        if (!isOpen) return null;

        return createPortal(
            <div
                className="fixed inset-0 bg-black bg-opacity-60 z-[9999] flex items-center justify-center"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    backdropFilter: 'blur(2px)'
                }}
                onClick={onClose}
            >
                <div
                    className="bg-white rounded-lg p-4 m-4 w-full max-w-sm relative shadow-2xl"
                    style={{
                        zIndex: 10000,
                        backgroundColor: '#ffffff',
                        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                        border: '1px solid rgba(0, 0, 0, 0.1)'
                    }}
                    onClick={(e) => e.stopPropagation()}
                >
                    <div className="flex items-center mb-4 border-b pb-3">
                        <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                            <span className="text-white font-bold">{group.title?.charAt(0)?.toUpperCase()}</span>
                        </div>
                        <div>
                            <h3 className="font-semibold">{group.title}</h3>
                            <p className="text-sm text-gray-500">{group.card_type_name}</p>
                        </div>
                    </div>

                    <div className="space-y-2 bg-gray-50 p-2 rounded-lg">
                        {/* View Members */}
                        <Link to={`/members/group?group-id=${group.id}`}>
                            <button
                                className="w-full flex items-center p-3 text-left bg-white hover:bg-blue-50 rounded-lg border border-gray-200"
                                onClick={onClose}
                            >
                                <FaRegEye className="mr-3 text-blue-500" size={18} />
                                <span>View Members</span>
                            </button>
                        </Link>

                        {/* Edit */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-green-50 rounded-lg border border-gray-200"
                            onClick={() => {
                                handleEditClick(group);
                                onClose();
                            }}
                        >
                            <FiEdit className="mr-3 text-green-500" size={18} />
                            <span>Edit Group</span>
                        </button>

                        {/* Delete */}
                        <button
                            className="w-full flex items-center p-3 text-left bg-white hover:bg-red-50 rounded-lg border border-gray-200 text-red-600"
                            onClick={() => {
                                handleDeleteClick(group);
                                onClose();
                            }}
                        >
                            <TfiTrash className="mr-3" size={18} />
                            <span>Delete Group</span>
                        </button>
                    </div>

                    <button
                        className="w-full mt-4 p-2 bg-gray-200 hover:bg-gray-300 rounded-lg text-center font-medium transition-colors"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                </div>
            </div>,
            document.body
        );
    };

    // Data Table Body Template
    // احذف الاستيراد التالي:
    // import { DataTable } from 'primereact/datatable';
    // import { Column } from 'primereact/column';
    // احذف تعريف الدوال غير المستخدمة:
    // const actionBodyTemplate = (rowData) => { ... }
    // const statusBodyTemplate = (rowData) => { ... }

    // Mobile list view component
    const MobileListView = () => {
        if (loading) {
            return (
                <div className="space-y-2">
                    {[...Array(5)].map((_, index) => (
                        <div key={index} className="bg-white border rounded-lg p-4 shadow-sm animate-pulse">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center flex-1">
                                    <div className="w-12 h-12 bg-gray-300 rounded-full mr-3"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/2 mb-1"></div>
                                        <div className="h-3 bg-gray-300 rounded w-1/3"></div>
                                    </div>
                                </div>
                                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8">
                    <p className="text-gray-500">No groups found</p>
                </div>
            );
        }

        return (
            <div className="space-y-2">
                {data.map((group) => {
                    const status = group.status || "inactive";
                    const backgroundColor = status === "active" ? "#22C55E" : status === "inactive" ? "#dc2626" : "#9ca3af";
                    return (
                    <div key={group.id} className="bg-white border rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center flex-1">
                                    <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center mr-3 text-white text-3xl font-bold shadow relative overflow-hidden">
                                        <span className="z-10">{group.title?.charAt(0)?.toUpperCase()}</span>
                                        <span className="absolute inset-0 flex items-center justify-center opacity-20 z-0">
                                            <FaUsers size={64} color="#fff" />
                                    </span>
                                </div>
                                <div className="flex-1">
                                        <h3 className="font-semibold text-lg text-gray-900 mb-1">{group.title}</h3>
                                        <p className="text-sm text-gray-500 mb-1">{group.description}</p>
                                        <div className="flex items-center gap-2 mb-1">
                                            <span className="inline-block bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-bold border border-blue-300">{group.card_type_name}</span>
                                            <span className="inline-block text-white rounded-[6px] font-bold text-xs py-1 px-2 capitalize" style={{ backgroundColor }}>{status}</span>
                                    </div>
                                </div>
                            </div>
                            <button
                                className="p-2 hover:bg-gray-100 rounded-full"
                                onClick={() => setMobileActionMenuOpen(group.id)}
                            >
                                <HiDotsVertical className="text-gray-500" size={20} />
                            </button>
                        </div>
                    </div>
                    );
                })}

                {/* Mobile Action Menu */}
                {mobileActionMenuOpen && (
                    <MobileActionMenu
                        group={data.find(g => g.id === mobileActionMenuOpen)}
                        isOpen={!!mobileActionMenuOpen}
                        onClose={() => setMobileActionMenuOpen(null)}
                    />
                )}
            </div>
        );
    };
    
    // const updateHandler = async (rowData) => {
    //     await getGroupMember.mutateAsync(rowData.id, {
    //         onSuccess: (data) => {
    //             setSelectedRow(rowData);
    //             setSelectedMembers({
    //                 groupData: rowData,
    //                 action: "update",
    //                 data: data,
    //             });
    //             navigate("/users/members")
    //         }
    //     })
    // }

    // const createGroup = () => {
        // setSelectedMembers({});
        // dialogHandler("createGroup");
        // console.log("Create Group Clicked")
    // }

    // const Header = () => {
    //     const activeBtn = 0; //isEmpty(selectedMembers.data);
    //     // const userRole = localStorage.getItem('user_role'); 
    //     return (
    //         <div className="w-full ">
    //             <button
    //                 className={`${activeBtn ? "gray-btn " : "main-btn"} text-md me-2 shadow-md`}
    //                 disabled={activeBtn} onClick={openCreateCardModal}>
    //                 Create Group
    //             </button>
    //         </div>
    //     )
    // }

    // تحقق من الحالات الخاصة قبل أي return للصفحة الأصلية
    if (subscriptionLoading) {
        return <p className="text-center">Loading...</p>;
    }
    if (subscriptionError && subscriptionError.message === "Your subscription has expired. Please renew your subscription to continue.") {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">Subscription Status</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Subscription Expired
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            {subscriptionError.message}
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Please renew your subscription to continue using our services.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-red-50 to-white"
                            style={{ borderTop: '6px solid #ef4444' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">Expired Plan</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-red-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-red-600 font-medium">Your subscription has expired</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-red-100 text-red-800 flex items-center justify-center">
                                <span className="font-medium">⚠️ Please renew your subscription to continue</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    Renew Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }
    if (noPackage) {
        return (
            <Container>
                <div className="relative isolate bg-white px-6 py-12 sm:py-16 lg:px-8">
                    <div className="absolute inset-x-0 -top-3 -z-10 transform-gpu overflow-hidden px-36 blur-3xl" aria-hidden="true">
                        <motion.div
                            className="mx-auto aspect-[1155/678] w-[72.1875rem] opacity-80"
                            style={{
                                clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                            }}
                            animate={{
                                background: [
                                    'linear-gradient(45deg, #ff2299 0%, #9922ff 100%)',
                                    'linear-gradient(45deg, #9922ff 0%, #22aaff 100%)',
                                    'linear-gradient(45deg, #22aaff 0%, #ff2299 100%)'
                                ],
                                rotate: -360,
                            }}
                            transition={{
                                duration: 20,
                                repeat: Infinity,
                                ease: "linear",
                            }}
                        />
                    </div>
                    <div className="mx-auto max-w-4xl text-center">
                        <h2 className="text-base font-semibold leading-7 text-indigo-600">No Package Found</h2>
                        <h1 className="mt-2 text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl">
                            Unlock All Features With a Package
                        </h1>
                        <p className="mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600">
                            We couldn&apos;t find any active package for your account. To access all features and design templates, please purchase a package.
                        </p>
                        <p className="mt-4 text-center text-gray-600">
                            Click the button below to explore available packages and unlock the full potential of your dashboard.
                        </p>
                    </div>
                    <div className="mt-16 mx-auto max-w-2xl">
                        <div className="relative rounded-3xl p-8 ring-1 ring-gray-900/10 shadow-xl bg-gradient-to-br from-blue-50 to-white"
                            style={{ borderTop: '6px solid #3b82f6' }}>
                            <h3 className="text-2xl font-bold text-gray-900 mb-2">No Active Package</h3>
                            <div className="flex items-center">
                                <svg className="h-5 w-5 flex-none text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                                </svg>
                                <span className="ml-3 text-blue-600 font-medium">No package is currently assigned to your account</span>
                            </div>
                            <div className="mt-6 p-3 rounded-lg bg-blue-100 text-blue-800 flex items-center justify-center">
                                <span className="font-medium">💡 Purchase a package to unlock all features</span>
                            </div>
                            <div className="mt-10 flex items-center justify-center gap-x-6">
                                <button
                                    onClick={() => window.location.href = `${import.meta.env.VITE_ENV}/manager/Packages`}
                                    className="main-btn text-md shadow-md px-5 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200"
                                >
                                    View Packages
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </Container>
        );
    }

    return (
        <React.Fragment>
        <div className="w-full min-w-full h-full flex flex-col">
            <Toast ref={toast} position="top-right" />     

            <ConfirmDialog                                       //This is a small confirm modal for the delete button
                group="headless"
                content={(options) => (
                    <div className="flex flex-col items-center p-5">
                        <i className="pi pi-exclamation-triangle text-6xl text-yellow-500 mb-3"/>
                        <span className="text-xl font-bold mb-2">{options.message}</span>
                        <div className="flex gap-3">
                            <button className="p-button p-component" onClick={options.accept}>
                                Yes
                            </button>
                            <button className="p-button p-component p-button-outlined" onClick={options.reject}>
                                No
                            </button>
                        </div>
                    </div>
                )}
            />

                {/* Search Bar Section */}
            <div className={`w-full mb-8 mt-1 flex justify-center items-center`}>
                <div className="flex flex-row-reverse gap-4 w-full max-w-[900px] items-center justify-center">
                    <div className="flex gap-2 min-w-[220px]">
                        <select
                            className="border rounded-md px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 text-gray-700 bg-white"
                            value={selectedType}
                            onChange={e => setSelectedType(e.target.value)}
                        >
                            <option value="">All Types</option>
                            {cardTypes.map(type => (
                                <option key={type} value={type}>{type}</option>
                            ))}
                        </select>
                        <select
                            className="border rounded-md px-3 py-2 shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 text-gray-700 bg-white"
                            value={selectedStatus}
                            onChange={e => setSelectedStatus(e.target.value)}
                        >
                            <option value="">All Statuses</option>
                            {statuses.map(status => (
                                <option key={status} value={status}>{status}</option>
                            ))}
                        </select>
                    </div>
                    <div className="relative flex-grow max-w-[350px]">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="h-4 w-4 text-gray-400" aria-hidden="true" />
                    </div>
                    <input
                        type="text"
                        placeholder="Search by group title..."
                            className="w-full pl-10 pr-4 py-2 border rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300 focus:border-blue-300 transition-all duration-200 text-sm"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        />
                </div>
            </div>
            </div>
            <div className="mb-8"></div>

             {/* Container for Table and Modals */}

                    {/* --- Render Create Group Modal --- */}
                    {/* Conditionally rendered ONLY when isCreateGroupModalOpen is true */}
                    {isCreateGroupModalOpen && (
                        <GroupForm
                            isModalOpen={isCreateGroupModalOpen}
                            setIsModalOpen={setisCreateGroupModalOpen} // Pass the setter for create modal
                            onSuccess={handleSuccess} // Pass the common success handler
                            // groupToEdit prop is omitted, so it runs in CREATE mode
                        />
                    )}

                    {/* --- Render Edit Group Modal --- */}
                    {/* Conditionally rendered ONLY when isEditGroupModalOpen is true AND groupBeingEdited has data */}
                    {isEditGroupModalOpen && groupBeingEdited && (
                        <GroupForm
                            isModalOpen={isEditGroupModalOpen}
                            setIsModalOpen={setIsEditGroupModalOpen} // Pass the setter for edit modal
                            onSuccess={handleSuccess} // Pass the common success handler
                            groupToEdit={groupBeingEdited} // Pass the data for the group to be edited
                        />
                    )}

                {/* Conditional rendering for mobile vs desktop */}
                <div className="flex-grow h-full">
                    {isMobile ? (
                        <MobileListView />
                    ) : (
                        <>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-8">
                            {filteredData && filteredData.length > 0 ? filteredData.map((group) => {
                                const status = group.status || "inactive";
                                const backgroundColor = status === "active" ? "#22C55E" : status === "inactive" ? "#dc2626" : "#9ca3af";
                                
                                // طباعة تفصيلية لبيانات المجموعة
                                console.log('بيانات المجموعة:', {
                                    id: group.id,
                                    title: group.title,
                                    users: group.users,
                                    members: group.members,
                                    allKeys: Object.keys(group),
                                    fullData: group
                                });
                                
                                // طباعة جميع المفاتيح بالتفصيل
                                console.log('جميع المفاتيح في المجموعة:', Object.keys(group));
                                Object.keys(group).forEach(key => {
                                    console.log(`${key}:`, group[key]);
                                });
                                
                                // البحث عن بيانات الأعضاء في أي مكان في البيانات
                                const allGroupData = JSON.stringify(group);
                                console.log('جميع بيانات المجموعة (JSON):', allGroupData);
                                
                                return (
                                    <div key={group.id} className="bg-white rounded-2xl shadow-lg p-8 flex flex-col border border-gray-200 hover:shadow-2xl transition relative">
                                        <div className="flex items-center mb-4 relative">
                                            <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center mr-3 text-white text-3xl font-bold shadow relative overflow-hidden">
                                                <span className="z-10">{group.title?.charAt(0)?.toUpperCase()}</span>
                                                <span className="absolute inset-0 flex items-center justify-center opacity-20 z-0">
                                                    <FaUsers size={64} color="#fff" />
                                                </span>
                                            </div>
                                            <div className="flex-1">
                                                <h3 className="font-semibold text-lg text-gray-900 mb-1">{group.title}</h3>
                                                <p className="text-sm text-gray-500 mb-1">{group.description}</p>
                                                <div className="flex items-center gap-2 mb-1">
                                                    <span className="inline-block bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-xs font-bold border border-blue-300">{group.card_type_name}</span>
                                                    <span className="inline-block text-white rounded-[6px] font-bold text-xs py-1 px-2 capitalize" style={{ backgroundColor }}>{status}</span>
                                                </div>
                                                {/* Avatar Group for group members */}
                                                {group.users && group.users.length > 0 && (
                                                  <div className="mt-4">
                                                    <div className="flex -space-x-3">
                                                      {group.users.slice(0, 5).map((member, idx) => {
                                                        // تحديد مصدر الصورة
                                                        const imageSrc = member.image || member.avatar || member.photo || member.profile_image || '';
                                                        const memberName = member.name || member.full_name || member.email || '?';
                                                        
                                                        return (
                                                          <div
                                                            key={idx}
                                                            className="relative group"
                                                            style={{ zIndex: 10 + idx }}
                                                          >
                                                            <Avatar
                                                              className="w-10 h-10 border-2 border-white bg-gray-100 shadow relative cursor-pointer transition-all duration-300 ease-out group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-blue-200 group-hover:border-blue-300 hover:z-20 group-hover:-translate-y-1"
                                                            >
                                                              <AvatarImage
                                                                src={imageSrc}
                                                                alt={memberName}
                                                                className="object-cover w-full h-full transition-all duration-300 group-hover:brightness-110 group-hover:contrast-110"
                                                              />
                                                              <AvatarFallback className="bg-blue-200 text-blue-900 font-bold transition-all duration-300 group-hover:bg-blue-300 group-hover:text-blue-800">
                                                                {memberName.charAt(0).toUpperCase()}
                                                              </AvatarFallback>
                                                            </Avatar>
                                                            {/* Custom Tooltip with enhanced animation */}
                                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gradient-to-r from-gray-800 to-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out group-hover:mb-2 scale-95 group-hover:scale-100 whitespace-nowrap z-50 shadow-xl border border-gray-600 group-hover:-translate-y-1">
                                                              <div className="font-medium">{memberName}</div>
                                                              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-700"></div>
                                                              {/* Glow effect */}
                                                              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
                                                            </div>
                                                            {/* Hover ring effect */}
                                                            <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-blue-400 transition-all duration-300 ease-out scale-110 opacity-0 group-hover:opacity-100 group-hover:scale-125 group-hover:-translate-y-1"></div>
                                                          </div>
                                                        );
                                                      })}
                                                      {/* Show remaining count if more than 5 members */}
                                                      {group.users.length > 5 && (
                                                        <div className="relative group">
                                                          <div className="w-10 h-10 border-2 border-white bg-gradient-to-br from-gray-400 to-gray-500 rounded-full shadow relative flex items-center justify-center cursor-pointer transition-all duration-300 ease-out group-hover:scale-110 group-hover:shadow-lg group-hover:shadow-gray-300 group-hover:border-gray-300 hover:z-20 group-hover:bg-gradient-to-br group-hover:from-gray-500 group-hover:to-gray-600 group-hover:-translate-y-1" style={{ zIndex: 15 }}>
                                                            <span className="text-white font-bold text-xs transition-all duration-300 group-hover:scale-110">
                                                              +{group.users.length - 5}
                                                            </span>
                                                          </div>
                                                          {/* Custom Tooltip for remaining count with enhanced animation */}
                                                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-3 px-3 py-2 bg-gradient-to-r from-gray-800 to-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 ease-out group-hover:mb-2 scale-95 group-hover:scale-100 whitespace-nowrap z-50 shadow-xl border border-gray-600 group-hover:-translate-y-1">
                                                            <div className="font-medium">{group.users.length - 5} more members</div>
                                                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-700"></div>
                                                            {/* Glow effect */}
                                                            <div className="absolute inset-0 bg-gradient-to-r from-gray-400 to-gray-500 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
                                                          </div>
                                                          {/* Hover ring effect */}
                                                          <div className="absolute inset-0 rounded-full border-2 border-transparent group-hover:border-gray-400 transition-all duration-300 ease-out scale-110 opacity-0 group-hover:opacity-100 group-hover:scale-125 group-hover:-translate-y-1"></div>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-2 gap-2 mt-auto">
                                            <Link to={`/members/group?group-id=${group.id}`} className="w-full">
                                                <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg font-medium transition text-xs">
                                                    <FaRegEye size={14} />
                                                    <span>Members</span>
                                                </button>
                                            </Link>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-green-50 hover:bg-green-100 text-green-700 rounded-lg font-medium transition text-xs" onClick={() => handleEditClick(group)}>
                                                <FiEdit size={14} />
                                                <span>Edit</span>
                                            </button>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg font-medium transition text-xs" onClick={() => handleDeleteClick(group)}>
                                                <TfiTrash size={14} />
                                                <span>Delete</span>
                                            </button>
                                            <button className="w-full flex items-center justify-center gap-2 py-2 px-3 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-lg font-medium transition text-xs" onClick={() => openDesignsModal(group)}>
                                                <FaPalette size={14} />
                                                <span>Designs</span>
                                            </button>
                                        </div>
                                    </div>
                                );
                            }) : null}
                        </div>
                        <div className="flex justify-center mt-12">
                            <div className="inline-flex items-center gap-2">
                                <button
                                    className="px-3 py-1 rounded border border-gray-300 bg-white hover:bg-gray-100 disabled:opacity-50 text-sm"
                                    onClick={() => dataHandler({ first: Math.max(0, (lazyParams?.first || 0) - (lazyParams?.rows || 5)), rows: lazyParams?.rows })}
                                    disabled={!lazyParams || (lazyParams.first || 0) === 0}
                                >Previous</button>
                                <span className="mx-2 text-gray-600 text-sm">Page {Math.floor((lazyParams?.first || 0) / (lazyParams?.rows || 5)) + 1} of {Math.ceil((totalRecords || 0) / (lazyParams?.rows || 5))}</span>
                                <button
                                    className="px-3 py-1 rounded border border-gray-300 bg-white hover:bg-gray-100 disabled:opacity-50 text-sm"
                                    onClick={() => dataHandler({ first: (lazyParams?.first || 0) + (lazyParams?.rows || 5), rows: lazyParams?.rows })}
                                    disabled={!lazyParams || (lazyParams.first || 0) + (lazyParams.rows || 5) >= (totalRecords || 0)}
                                >Next</button>
                            </div>
                        </div>
                        </>
                    )}
                </div>
                {/* Dialog لعرض التصاميم */}
                <Dialog visible={isDesignsModalOpen} onHide={closeDesignsModal} header="Group Designs" style={{ width: '700px', maxWidth: '98vw' }} modal className="group-designs-modal">
                    {designsLoading ? (
                        <div className="flex justify-center items-center py-10"><i className="pi pi-spin pi-spinner text-2xl text-blue-500"></i></div>
                    ) : groupDesigns.length === 0 ? (
                        <div className="text-center text-gray-500 py-8">No designs found for this group.</div>
                    ) : (
                        <div>
                            {/* Search Field */}
                            <div className="mb-4 flex items-center gap-2">
                                <input
                                    type="text"
                                    className="w-full px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-300"
                                    placeholder="Search designs..."
                                    value={searchDesign}
                                    onChange={e => setSearchDesign(e.target.value)}
                                />
                            </div>
                            {/* Table */}
                            <div className="overflow-x-auto max-h-[350px]">
                                <table className="min-w-full border border-gray-200 rounded-lg bg-white">
                                    <thead>
                                        <tr className="bg-gray-50">
                                            <th className="p-2 text-left"><input type="checkbox" checked={filteredDesigns.length > 0 && filteredDesigns.every(d => selectedDesigns.some(s => s.id === d.id))} onChange={e => {
                                                if (e.target.checked) {
                                                    setSelectedDesigns(filteredDesigns);
                                                } else {
                                                    setSelectedDesigns([]);
                                                }
                                            }} /></th>
                                            <th className="p-2 text-left">Design Name</th>
                                            <th className="p-2 text-left">Type</th>
                                            {/* يمكنك إضافة أعمدة أخرى هنا */}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {filteredDesigns.map(design => {
                                            const name = design.title || design.name;
                                            const isChecked = selectedDesigns.some(s => s.id === design.id);
                                            const type = design.type || design.card_type?.type_of_connection || design.card_type_name || "-";
                                            return (
                                                <tr
                                                    key={design.id}
                                                    className={`border-b hover:bg-blue-50 transition cursor-pointer ${isChecked ? 'bg-blue-100' : ''}`}
                                                    onClick={() => {
                                                        if (isChecked) {
                                                            setSelectedDesigns(prev => prev.filter(s => s.id !== design.id));
                                                        } else {
                                                            setSelectedDesigns(prev => [...prev, design]);
                                                        }
                                                    }}
                                                >
                                                    <td className="p-2" onClick={e => e.stopPropagation()}>
                                                        <input
                                                            type="checkbox"
                                                            checked={isChecked}
                                                            onChange={e => {
                                                                if (e.target.checked) {
                                                                    setSelectedDesigns(prev => [...prev, design]);
                                                                } else {
                                                                    setSelectedDesigns(prev => prev.filter(s => s.id !== design.id));
                                                                }
                                                            }}
                                                            className="accent-blue-600 focus:ring-blue-400"
                                                        />
                                                    </td>
                                                    <td className="p-2 flex items-center gap-3">
                                                        {design.image || design.thumbnail ? (
                                                            <img src={design.image || design.thumbnail} alt={name} className="w-10 h-10 rounded" />
                                                        ) : (
                                                            <div
                                                                className="w-10 h-10 rounded-full flex items-center justify-center font-bold text-white text-lg shadow"
                                                                style={{
                                                                    background: `linear-gradient(135deg, #7f9cf5 0%, #a78bfa 100%)`
                                                                }}
                                                            >
                                                                {name?.charAt(0)?.toUpperCase() || "?"}
                                                            </div>
                                                        )}
                                                        <span className="font-medium">{name}</span>
                                                    </td>
                                                    <td className="p-2">{type}</td>
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                </table>
                            </div>
                            {/* Save Button (اختياري) */}
                            <div className="mt-4 flex flex-col items-end gap-2">
                                {saveMessage && <div className="text-green-600 font-medium mb-2">{saveMessage}</div>}
                                {saveError && <div className="text-red-600 font-medium mb-2">{saveError}</div>}
                                <button
                                    className="px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition disabled:opacity-60"
                                    onClick={handleSaveDesigns}
                                    disabled={saveLoading || selectedDesigns.length === 0}
                                >
                                    {saveLoading ? "Saving..." : "Save"}
                                </button>
                            </div>
                        </div>
                    )}
                </Dialog>
                    {/* Image Generation Modal */}
                    {showImageGenerationModal && (
                        <ImageGenerationModal
                            visible={showImageGenerationModal}
                            designId={currentDesignId}
                            batchId={currentBatchId}
                            useGroupsDesignApi={true}
                            onHide={() => {
                                setShowImageGenerationModal(false);
                                setCurrentBatchId(null);
                                setCurrentDesignId(null);
                            }}
                        />
                    )}
            </div>
            </React.Fragment>
    );
}

export default GroupsDataTable;