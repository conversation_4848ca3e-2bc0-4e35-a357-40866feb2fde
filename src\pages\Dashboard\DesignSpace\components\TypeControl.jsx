import { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { useQueryClient } from 'react-query';
import PropTypes from 'prop-types';

import { Dropdown } from 'primereact/dropdown';

import { useDesignSpace } from '@contexts/DesignSpaceContext';
import { useGetCardsTypes } from "@quires/template";
import { isEmpty } from 'lodash';
import axiosInstance from '../../../../config/Axios';

// Default card types if API fails
const DEFAULT_CARD_TYPES = [
    {
        id: 'default-business-card',
        name: 'Business Card',
        setting: {
            id: 'default-business-card',
            width: 350,
            height: 200
        }
    },
    {
        id: 'default-instagram',
        name: 'Instagram Post',
        setting: {
            id: 'default-instagram',
            width: 1080,
            height: 1080
        }
    },
    {
        id: 'default-id-badge',
        name: 'ID Badge',
        setting: {
            id: 'default-id-badge',
            width: 300,
            height: 450
        }
    }
];

function TypeControl({ hideLabel = false }) {
    const queryClient = useQueryClient();
    const { data: cardsTypesData, isLoading } = useGetCardsTypes();

    const { cardType, setCardType, groupId, setGroupId } = useDesignSpace();
    
    // Add logging to verify context values
    console.log('TypeControl - Context values:', {
        cardType: cardType?.id,
        groupId: groupId,
        setGroupId: typeof setGroupId
    });
    
    const [typesOptions, setTypesOptions] = useState(DEFAULT_CARD_TYPES);
    const [groups, setGroups] = useState([]);
    const [groupsLoading, setGroupsLoading] = useState(false);
    
    // Add ref to track if groupId was set from loaded data
    const initialGroupIdRef = useRef(null);
    const hasProcessedInitialGroupId = useRef(false);

    // Track initial groupId when component mounts or groupId changes
    useEffect(() => {
        if (groupId !== undefined && groupId !== null && !hasProcessedInitialGroupId.current) {
            initialGroupIdRef.current = groupId;
            hasProcessedInitialGroupId.current = true;
            console.log('TypeControl - Initial groupId set:', groupId);
        }
    }, [groupId]);

    // Reset the flag when component unmounts or when navigating to a different design
    useEffect(() => {
        return () => {
            hasProcessedInitialGroupId.current = false;
            initialGroupIdRef.current = null;
        };
    }, []);

    // Ensure groupId is preserved when groups are loaded for the first time
    useEffect(() => {
        if (groups.length > 0 && initialGroupIdRef.current !== null && initialGroupIdRef.current !== undefined) {
            const initialGroupExists = groups.some(group => group.id === initialGroupIdRef.current);
            if (initialGroupExists && groupId !== initialGroupIdRef.current) {
                console.log('Restoring initial groupId after groups loaded:', initialGroupIdRef.current);
                setGroupId(initialGroupIdRef.current);
            }
        }
    }, [groups, groupId, setGroupId]);

    // Get data from query client or from direct hook
    useEffect(() => {
        try {
            const cachedData = queryClient.getQueryData('getCardsTypes');
            if (cachedData && Array.isArray(cachedData) && cachedData.length > 0) {
                setTypesOptions(cachedData);
            } else if (cardsTypesData && Array.isArray(cardsTypesData) && cardsTypesData.length > 0) {
                setTypesOptions(cardsTypesData);
            } else {
                console.log('Using default card types');
                setTypesOptions(DEFAULT_CARD_TYPES);
            }
        } catch (error) {
            console.error('Error loading card types:', error);
            setTypesOptions(DEFAULT_CARD_TYPES);
        }
    }, [queryClient, cardsTypesData]);

    useEffect(() => {
        console.log('useEffect triggered - cardType changed:', cardType?.id);
        if (cardType && cardType.id) {
            setGroupsLoading(true);
            console.log('Fetching groups for card_type_id:', cardType.id); 
            axiosInstance.get(`/groups?card_type_id=${cardType.id}`)
                .then(res => {
                    console.log('Groups API response:', res.data);
                    const data = Array.isArray(res.data) ? res.data : (res.data.data || []);
                    console.log('Processed groups data:', data);
                    const newGroups = [{ id: 0, title: 'All Groups' }, ...data];
                    setGroups(newGroups);
                    
                    // Check if current groupId is still valid for the new cardType
                    if (groupId && groupId !== null && groupId !== undefined) {
                        const groupExists = newGroups.some(group => group.id === groupId);
                        if (!groupExists) {
                            console.log('Current groupId is not valid for new cardType, resetting to undefined');
                            setGroupId(undefined);
                        } else {
                            console.log('Current groupId is still valid, keeping it');
                        }
                    } else if (initialGroupIdRef.current !== null && initialGroupIdRef.current !== undefined) {
                        // Check if the initial groupId from loaded data is valid for this cardType
                        const initialGroupExists = newGroups.some(group => group.id === initialGroupIdRef.current);
                        if (initialGroupExists) {
                            console.log('Setting groupId to initial value from loaded data:', initialGroupIdRef.current);
                            setGroupId(initialGroupIdRef.current);
                        } else {
                            console.log('Initial groupId is not valid for this cardType, setting to undefined');
                            setGroupId(undefined);
                        }
                    } else {
                        console.log('No current groupId and no initial groupId, setting to undefined');
                        setGroupId(undefined);
                    }
                })
                .catch((error) => {
                    console.error('Error fetching groups:', error);
                    setGroups([{ id: 0, title: 'All Groups' }]);
                    // Don't reset groupId on error if we have an initial value
                    if (initialGroupIdRef.current === null || initialGroupIdRef.current === undefined) {
                        setGroupId(undefined);
                    }
                })
                .finally(() => setGroupsLoading(false));
        } else {
            console.log('No cardType or cardType.id, setting default groups');
            setGroups([{ id: 0, title: 'All Groups' }]);
            // Don't reset groupId if we have an initial value
            if (initialGroupIdRef.current === null || initialGroupIdRef.current === undefined) {
                setGroupId(undefined);
            }
        }
    }, [cardType]); // Remove setGroupId from dependencies to avoid infinite loop

    // Create options from typesOptions with safe fallbacks
    const options = useMemo(() => {
        try {
            if (!typesOptions || !Array.isArray(typesOptions) || typesOptions.length === 0) {
                return DEFAULT_CARD_TYPES;
            }

            return typesOptions.map(item => ({
                name: item.name || 'Unnamed Card',
                setting: {
                    id: item.id || `card-${item.name?.replace(/\s+/g, '-').toLowerCase() || 'unnamed'}`,
                    width: (item.setting?.width) || 300,
                    height: (item.setting?.height) || 200
                }
            }));
        } catch (error) {
            console.error('Error creating options:', error);
            return DEFAULT_CARD_TYPES;
        }
    }, [typesOptions]);

    // Set default card type if none is selected - only runs once when options are loaded
    useEffect(() => {
        try {
            // Only set card type if it's empty and we have options
            if (isEmpty(cardType) && options && options.length > 0) {
                setCardType({ ...options[0]?.setting });
            }
        } catch (error) {
            console.error('Error setting default card type:', error);
            if (DEFAULT_CARD_TYPES.length > 0) {
                setCardType({ ...DEFAULT_CARD_TYPES[0].setting });
            }
        }
        // No cleanup function to avoid resetting card type
        // This effect should only run when options change
    }, [options]); // Remove cardType and setCardType dependencies
    // Loading and error states
    const isDataReady = options && options.length > 0;

    // Memoize dropdown options to prevent re-renders
    const dropdownOptions = useMemo(() => {
        return isDataReady ? options : DEFAULT_CARD_TYPES;
    }, [isDataReady, options]);

    // Handle dropdown change safely - memoized to prevent re-renders
    const handleDropdownChange = useCallback((e) => {
        try {
            if (e && e.value) {
                setCardType(e.value, true); // true يعني أن المستخدم اختار فعلاً
            }
        } catch (error) {
            console.error('Error changing card type:', error);
        }
    }, [setCardType]);

    // Memoize the entire dropdown component to prevent re-renders
    const dropdownComponent = useMemo(() => {
        if (isLoading) {
            return <div className="rounded-[6px] me-3 text-sm bg-gray-100 p-2 text-gray-500">Loading...</div>;
        }

        return (
            <Dropdown
                className='rounded-[6px] me-3 text-[black] text-sm'
                optionLabel="name"
                optionValue="setting"
                value={cardType}
                options={dropdownOptions}
                onChange={handleDropdownChange}
                placeholder="Select a Type"
                panelClassName="text-sm"
            />
        );
    }, [isLoading, cardType, dropdownOptions, handleDropdownChange]);

    const groupDropdown = (
        <Dropdown
            className="rounded-[6px] me-3 text-[black] text-sm min-w-[180px]"
            optionLabel="title"
            optionValue="id"
            value={groupId}
            options={groups}
            onChange={e => {
                console.log('Group dropdown changed:', e.value);
                console.log('Previous groupId was:', groupId);
                console.log('Available groups:', groups);
                setGroupId(e.value);
            }}
            placeholder="Select Group"
            panelClassName="text-sm"
            filter
            showClear
            loading={groupsLoading}
            key={`group-dropdown-${cardType?.id}-${groups.length}`} // Add key to force re-render when cardType or groups change
        />
    );

    // Add logging to show current state
    console.log('TypeControl render - Current state:', {
        cardType: cardType?.id,
        groupId: groupId,
        groupsCount: groups.length,
        groupsLoading: groupsLoading
    });

    return (
        <div className="flex flex-col">
            {!hideLabel && <label className="mr-1 text-sm">Card Type</label>}
            <div className="flex flex-row items-center gap-2">
                {dropdownComponent}
                {groupDropdown}
            </div>
        </div>
    )
}

TypeControl.propTypes = {
    hideLabel: PropTypes.bool,
};

export default TypeControl