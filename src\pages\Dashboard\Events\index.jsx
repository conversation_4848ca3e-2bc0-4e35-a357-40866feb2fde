import React, { useState, useEffect, useMemo, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import { confirmDialog, ConfirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { Calendar } from 'primereact/calendar';
import { motion } from 'framer-motion';
import { FiPlus, FiSearch, FiCalendar, FiUsers, FiClock, FiMapPin } from 'react-icons/fi';
import { HiDotsVertical } from 'react-icons/hi';
import { TfiTrash } from 'react-icons/tfi';
import { FiEdit } from 'react-icons/fi';

import Container from '@components/Container';
import { useLayout } from '@contexts/LayoutContext';
import { mockEvents, eventStatuses } from '@data/mockEventsData';
import CreateEventModal from './components/CreateEventModal';
import EventDetailsModal from './components/EventDetailsModal';
import AssignCardsModal from './components/AssignCardsModal';
import AssociatedGroupsModal from './components/AssociatedGroupsModal';
import './Events.css';

function EventsIndex() {
    const { isMobile } = useLayout();
    const toast = useRef(null);

    // State management
    const [events, setEvents] = useState(mockEvents);
    const [loading, setLoading] = useState(false);
    const [globalFilter, setGlobalFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState(null);
    const [dateFilter, setDateFilter] = useState(null);
    const [selectedEvent, setSelectedEvent] = useState(null);

    // Modal states
    const [createEventModalVisible, setCreateEventModalVisible] = useState(false);
    const [eventDetailsModalVisible, setEventDetailsModalVisible] = useState(false);
    const [assignCardsModalVisible, setAssignCardsModalVisible] = useState(false);
    const [associatedGroupsModalVisible, setAssociatedGroupsModalVisible] = useState(false);
    const [mobileActionMenuOpen, setMobileActionMenuOpen] = useState(null);

    // Filtered events based on search and filters
    const filteredEvents = useMemo(() => {
        let filtered = events;

        // Global search filter
        if (globalFilter) {
            filtered = filtered.filter(event =>
                event.name.toLowerCase().includes(globalFilter.toLowerCase()) ||
                event.description.toLowerCase().includes(globalFilter.toLowerCase()) ||
                event.location.toLowerCase().includes(globalFilter.toLowerCase()) ||
                event.createdBy.toLowerCase().includes(globalFilter.toLowerCase())
            );
        }

        // Status filter
        if (statusFilter) {
            filtered = filtered.filter(event => event.status === statusFilter.value);
        }

        // Date filter
        if (dateFilter) {
            const filterDate = new Date(dateFilter).toISOString().split('T')[0];
            filtered = filtered.filter(event =>
                event.startDate === filterDate || event.endDate === filterDate
            );
        }

        return filtered;
    }, [events, globalFilter, statusFilter, dateFilter]);

    // Event handlers
    const handleCreateEvent = (eventData) => {
        const newEvent = {
            ...eventData,
            id: Math.max(...events.map(e => e.id)) + 1,
            createdAt: new Date().toISOString(),
            createdBy: "Current User", // In real app, get from auth context
            currentAttendees: 0,
            temporaryCards: []
        };
        setEvents([...events, newEvent]);
        setCreateEventModalVisible(false);
        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Event created successfully',
            life: 3000
        });
    };

    const handleEditEvent = (event) => {
        setSelectedEvent(event);
        setCreateEventModalVisible(true);
    };

    const handleUpdateEvent = (updatedEvent) => {
        setEvents(events.map(event =>
            event.id === updatedEvent.id ? updatedEvent : event
        ));

        // Update the selected event if it's the one being updated
        if (selectedEvent && selectedEvent.id === updatedEvent.id) {
            setSelectedEvent(updatedEvent);
        }

        // Only close the create modal if it's open
        if (createEventModalVisible) {
            setCreateEventModalVisible(false);
            setSelectedEvent(null);
        }

        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Event updated successfully',
            life: 3000
        });
    };

    const handleDeleteEvent = (event) => {
        confirmDialog({
            message: `Are you sure you want to delete "${event.name}"? This will also remove all associated temporary card assignments.`,
            header: 'Delete Event Confirmation',
            icon: 'pi pi-exclamation-triangle',
            acceptClassName: 'p-button-danger',
            acceptLabel: 'Yes, Delete',
            rejectLabel: 'Cancel',
            accept: () => {
                setEvents(events.filter(e => e.id !== event.id));
                toast.current.show({
                    severity: 'success',
                    summary: 'Success',
                    detail: 'Event deleted successfully',
                    life: 3000
                });
            }
        });
    };

    const handleViewDetails = (event) => {
        setSelectedEvent(event);
        setEventDetailsModalVisible(true);
    };

    const handleAssignCards = (event) => {
        setSelectedEvent(event);
        setAssignCardsModalVisible(true);
    };

    const handleCardAssignment = (eventId, cardAssignments) => {
        setEvents(events.map(event =>
            event.id === eventId
                ? { ...event, temporaryCards: [...event.temporaryCards, ...cardAssignments] }
                : event
        ));
        setAssignCardsModalVisible(false);
        toast.current.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Cards assigned successfully',
            life: 3000
        });
    };

    const handleViewAssociatedGroups = (event) => {
        setSelectedEvent(event);
        setAssociatedGroupsModalVisible(true);
    };

    // Template functions for DataTable columns
    const statusBodyTemplate = (rowData) => {
        const status = eventStatuses.find(s => s.value === rowData.status);
        return (
            <Tag
                value={status?.label}
                style={{ backgroundColor: status?.color }}
                className="text-white px-2 py-1 rounded-full text-xs"
            />
        );
    };

    const dateBodyTemplate = (rowData) => {
        const startDate = new Date(rowData.startDate).toLocaleDateString();
        const endDate = new Date(rowData.endDate).toLocaleDateString();
        return (
            <div className="flex items-center gap-1">
                <FiCalendar size={14} className="text-gray-500" />
                <span className="text-sm">
                    {startDate === endDate ? startDate : `${startDate} - ${endDate}`}
                </span>
            </div>
        );
    };

    const attendeesBodyTemplate = (rowData) => {
        const percentage = (rowData.currentAttendees / rowData.maxAttendees) * 100;
        return (
            <div className="flex items-center gap-2">
                <FiUsers size={14} className="text-gray-500" />
                <span className="text-sm">
                    {rowData.currentAttendees}/{rowData.maxAttendees}
                </span>
                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                </div>
            </div>
        );
    };

    const cardsBodyTemplate = (rowData) => {
        return (
            <div className="flex items-center gap-1">
                <span className="text-sm font-medium">
                    {rowData.temporaryCards?.length || 0}
                </span>
                <span className="text-xs text-gray-500">cards</span>
            </div>
        );
    };

    const associatedGroupsBodyTemplate = (rowData) => {
        const groupCount = rowData.associatedGroups?.length || 0;
        return (
            <div
                className="flex items-center gap-1 cursor-pointer hover:text-blue-600 transition-colors"
                onClick={() => handleViewAssociatedGroups(rowData)}
            >
                <span className="text-sm font-medium text-blue-600">
                    {groupCount}
                </span>
                <span className="text-xs text-gray-500">
                    {groupCount === 1 ? 'group' : 'groups'}
                </span>
            </div>
        );
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <div className="relative">
                <Button
                    icon={<HiDotsVertical />}
                    className="p-button-text p-button-rounded"
                    onClick={(e) => {
                        e.stopPropagation();
                        setMobileActionMenuOpen(mobileActionMenuOpen === rowData.id ? null : rowData.id);
                    }}
                />
                {mobileActionMenuOpen === rowData.id && (
                    <ActionDropdownMenu
                        event={rowData}
                        onClose={() => setMobileActionMenuOpen(null)}
                        onEdit={() => {
                            handleEditEvent(rowData);
                            setMobileActionMenuOpen(null);
                        }}
                        onDelete={() => {
                            handleDeleteEvent(rowData);
                            setMobileActionMenuOpen(null);
                        }}
                        onViewDetails={() => {
                            handleViewDetails(rowData);
                            setMobileActionMenuOpen(null);
                        }}
                        onAssignCards={() => {
                            handleAssignCards(rowData);
                            setMobileActionMenuOpen(null);
                        }}
                    />
                )}
            </div>
        );
    };

    // Action Dropdown Menu Component
    const ActionDropdownMenu = ({ event, onClose, onEdit, onDelete, onViewDetails, onAssignCards }) => {
        return (
            <>
                <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-48">
                    <div className="py-2">
                        <button
                            className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                            onClick={onViewDetails}
                        >
                            <i className="pi pi-eye text-blue-500" />
                            View Details
                        </button>
                        <button
                            className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                            onClick={onEdit}
                        >
                            <FiEdit className="text-green-500" />
                            Edit Event
                        </button>
                        <button
                            className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2"
                            onClick={onAssignCards}
                        >
                            <i className="pi pi-id-card text-purple-500" />
                            Assign Cards
                        </button>
                        <hr className="my-1" />
                        <button
                            className="w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center gap-2 text-red-600"
                            onClick={onDelete}
                        >
                            <TfiTrash />
                            Delete Event
                        </button>
                    </div>
                </div>
                <div
                    className="fixed inset-0 z-40"
                    onClick={onClose}
                />
            </>
        );
    };

    return (
        <Container>
            <Toast ref={toast} position="top-right" />

            {/* Header Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6"
            >
                <div>
                    <h1 className="text-2xl font-bold text-gray-800">Events Management</h1>
                    <p className="text-gray-600 mt-1">Create and manage events with temporary card assignments</p>
                </div>

                <Button
                    label={isMobile ? "Create" : "Create Event"}
                    icon={<FiPlus size={16} />}
                    className="main-btn"
                    onClick={() => setCreateEventModalVisible(true)}
                />
            </motion.div>

            {/* Filters Section */}
            <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6"
            >
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="relative">
                        <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                        <InputText
                            placeholder="Search events..."
                            value={globalFilter}
                            onChange={(e) => setGlobalFilter(e.target.value)}
                            className="w-full pl-10"
                        />
                    </div>

                    <Dropdown
                        value={statusFilter}
                        options={eventStatuses}
                        onChange={(e) => setStatusFilter(e.value)}
                        placeholder="Filter by status"
                        showClear
                        className="w-full"
                    />

                    <Calendar
                        value={dateFilter}
                        onChange={(e) => setDateFilter(e.value)}
                        placeholder="Filter by date"
                        showIcon
                        showClear
                        className="w-full"
                    />

                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">
                            {filteredEvents.length} of {events.length} events
                        </span>
                    </div>
                </div>
            </motion.div>

            {/* Events Table/List */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="bg-white rounded-lg shadow-sm border border-gray-200"
            >
                {isMobile ? (
                    <MobileEventsList
                        events={filteredEvents}
                        onEdit={handleEditEvent}
                        onDelete={handleDeleteEvent}
                        onViewDetails={handleViewDetails}
                        onAssignCards={handleAssignCards}
                    />
                ) : (
                    <DataTable
                        value={filteredEvents}
                        loading={loading}
                        paginator
                        rows={10}
                        rowsPerPageOptions={[5, 10, 25, 50]}
                        className="border-t-0"
                        emptyMessage="No events found"
                        responsiveLayout="stack"
                        breakpoint="960px"
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} events"
                    >
                        <Column field="name" header="Event Name" sortable className="font-medium" />
                        <Column body={dateBodyTemplate} header="Date" sortable sortField="startDate" />
                        <Column body={statusBodyTemplate} header="Status" sortable sortField="status" />
                        <Column body={attendeesBodyTemplate} header="Attendees" />
                        <Column body={cardsBodyTemplate} header="Cards" />
                        <Column body={associatedGroupsBodyTemplate} header="Groups" />
                        <Column field="location" header="Location" />
                        <Column body={actionBodyTemplate} exportable={false} style={{ minWidth: '8rem' }} />
                    </DataTable>
                )}
            </motion.div>

            {/* Modals */}
            <CreateEventModal
                visible={createEventModalVisible}
                onHide={() => {
                    setCreateEventModalVisible(false);
                    setSelectedEvent(null);
                }}
                onSave={selectedEvent ? handleUpdateEvent : handleCreateEvent}
                eventData={selectedEvent}
                isEdit={!!selectedEvent}
            />

            <EventDetailsModal
                visible={eventDetailsModalVisible}
                onHide={() => {
                    setEventDetailsModalVisible(false);
                    setSelectedEvent(null);
                }}
                event={selectedEvent}
                onEdit={handleEditEvent}
                onAssignCards={handleAssignCards}
            />

            <AssignCardsModal
                visible={assignCardsModalVisible}
                onHide={() => {
                    setAssignCardsModalVisible(false);
                    setSelectedEvent(null);
                }}
                event={selectedEvent}
                onAssign={handleCardAssignment}
            />

            <AssociatedGroupsModal
                visible={associatedGroupsModalVisible}
                onHide={() => {
                    setAssociatedGroupsModalVisible(false);
                    setSelectedEvent(null);
                }}
                event={selectedEvent}
                onUpdateEvent={handleUpdateEvent}
            />

            <ConfirmDialog />
        </Container>
    );
}

// Mobile Events List Component
const MobileEventsList = ({ events, onEdit, onDelete, onViewDetails, onAssignCards }) => {
    return (
        <div className="p-4">
            <div className="space-y-4">
                {events.map((event) => (
                    <div key={event.id} className="border border-gray-200 rounded-lg p-4 bg-white">
                        <div className="flex justify-between items-start mb-2">
                            <h3 className="font-semibold text-gray-900 text-lg">{event.name}</h3>
                            <Tag
                                value={eventStatuses.find(s => s.value === event.status)?.label}
                                style={{ backgroundColor: eventStatuses.find(s => s.value === event.status)?.color }}
                                className="text-white px-2 py-1 rounded-full text-xs"
                            />
                        </div>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{event.description}</p>

                        <div className="space-y-2 mb-4">
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiCalendar size={14} />
                                <span>
                                    {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                                </span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiMapPin size={14} />
                                <span>{event.location}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <FiUsers size={14} />
                                <span>{event.currentAttendees}/{event.maxAttendees} attendees</span>
                            </div>
                        </div>

                        <div className="flex gap-2 flex-wrap">
                            <Button
                                label="Details"
                                size="small"
                                className="p-button-outlined p-button-sm"
                                onClick={() => onViewDetails(event)}
                            />
                            <Button
                                label="Edit"
                                size="small"
                                className="p-button-outlined p-button-sm"
                                onClick={() => onEdit(event)}
                            />
                            <Button
                                label="Cards"
                                size="small"
                                className="p-button-outlined p-button-sm"
                                onClick={() => onAssignCards(event)}
                            />
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default EventsIndex;